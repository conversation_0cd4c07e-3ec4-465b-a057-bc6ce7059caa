<!DOCTYPE html>
<html lang="es" class="h-full bg-gray-50">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Marketplace de Integraciones - Arroyo University</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
    </style>
</head>
<body class="h-full">
    <div class="min-h-full">
        <!-- Navigation -->
        <nav class="bg-white shadow">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <button onclick="window.location.href='04_admin_tenant_dashboard.html'" 
                                class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center hover:bg-blue-700 transition-colors">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.84L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3z"/>
                            </svg>
                        </button>
                        <h1 class="ml-3 text-xl font-semibold text-gray-900">Universidad Ejemplo</h1>
                        <nav class="ml-8 flex space-x-4">
                            <a href="04_admin_tenant_dashboard.html" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">Dashboard</a>
                            <a href="36_api_documentation.html" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">API</a>
                            <a href="#" class="bg-blue-100 text-blue-700 px-3 py-2 rounded-md text-sm font-medium">Integraciones</a>
                        </nav>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                            <span class="text-sm font-medium text-gray-700">AM</span>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="mb-8">
                <div class="flex items-center justify-between">
                    <div>
                        <h2 class="text-2xl font-bold text-gray-900">Marketplace de Integraciones</h2>
                        <p class="mt-1 text-gray-600">Conecta Arroyo University con tus herramientas favoritas</p>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <div class="relative">
                            <input type="text" placeholder="Buscar integraciones..."
                                   class="w-64 px-4 py-2 pl-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <svg class="absolute left-3 top-2.5 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                            </svg>
                        </div>
                        <button class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"/>
                            </svg>
                            Filtros
                        </button>
                    </div>
                </div>
            </div>

            <!-- Categories -->
            <div class="mb-8">
                <div class="flex flex-wrap gap-2">
                    <button class="px-4 py-2 bg-blue-100 text-blue-700 rounded-full text-sm font-medium">Todas</button>
                    <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-full text-sm font-medium hover:bg-gray-200">LMS</button>
                    <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-full text-sm font-medium hover:bg-gray-200">Comunicación</button>
                    <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-full text-sm font-medium hover:bg-gray-200">Analítica</button>
                    <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-full text-sm font-medium hover:bg-gray-200">Productividad</button>
                    <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-full text-sm font-medium hover:bg-gray-200">Autenticación</button>
                </div>
            </div>

            <!-- Featured Integrations -->
            <div class="mb-8">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Integraciones Destacadas</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- Moodle Integration -->
                    <div class="bg-white shadow rounded-lg overflow-hidden">
                        <div class="p-6">
                            <div class="flex items-center mb-4">
                                <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mr-4">
                                    <span class="text-orange-600 font-bold text-lg">M</span>
                                </div>
                                <div>
                                    <h4 class="text-lg font-medium text-gray-900">Moodle</h4>
                                    <p class="text-sm text-gray-500">LMS Integration</p>
                                </div>
                            </div>
                            <p class="text-gray-600 text-sm mb-4">
                                Sincroniza estudiantes, cursos y resultados automáticamente con tu plataforma Moodle.
                            </p>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Conectado
                                    </span>
                                    <span class="text-xs text-gray-500">2,341 usuarios</span>
                                </div>
                                <button onclick="configureIntegration('moodle')" 
                                        class="text-blue-600 hover:text-blue-500 text-sm font-medium">
                                    Configurar
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Slack Integration -->
                    <div class="bg-white shadow rounded-lg overflow-hidden">
                        <div class="p-6">
                            <div class="flex items-center mb-4">
                                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                                    <span class="text-purple-600 font-bold text-lg">#</span>
                                </div>
                                <div>
                                    <h4 class="text-lg font-medium text-gray-900">Slack</h4>
                                    <p class="text-sm text-gray-500">Comunicación</p>
                                </div>
                            </div>
                            <p class="text-gray-600 text-sm mb-4">
                                Recibe notificaciones de exámenes completados y resultados directamente en Slack.
                            </p>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        Disponible
                                    </span>
                                    <span class="text-xs text-gray-500">1,892 usuarios</span>
                                </div>
                                <button onclick="installIntegration('slack')" 
                                        class="px-4 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700">
                                    Instalar
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Google Classroom -->
                    <div class="bg-white shadow rounded-lg overflow-hidden">
                        <div class="p-6">
                            <div class="flex items-center mb-4">
                                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                                    <span class="text-green-600 font-bold text-lg">G</span>
                                </div>
                                <div>
                                    <h4 class="text-lg font-medium text-gray-900">Google Classroom</h4>
                                    <p class="text-sm text-gray-500">LMS Integration</p>
                                </div>
                            </div>
                            <p class="text-gray-600 text-sm mb-4">
                                Importa clases y estudiantes desde Google Classroom automáticamente.
                            </p>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        Disponible
                                    </span>
                                    <span class="text-xs text-gray-500">3,156 usuarios</span>
                                </div>
                                <button onclick="installIntegration('google-classroom')" 
                                        class="px-4 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700">
                                    Instalar
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- All Integrations -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Todas las Integraciones</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Microsoft Teams -->
                        <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                                    <span class="text-blue-600 font-bold">T</span>
                                </div>
                                <div>
                                    <h4 class="text-sm font-medium text-gray-900">Microsoft Teams</h4>
                                    <p class="text-xs text-gray-500">Comunicación • 1,234 usuarios</p>
                                </div>
                            </div>
                            <button onclick="installIntegration('teams')" 
                                    class="px-3 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700">
                                Instalar
                            </button>
                        </div>

                        <!-- Canvas LMS -->
                        <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center mr-4">
                                    <span class="text-red-600 font-bold">C</span>
                                </div>
                                <div>
                                    <h4 class="text-sm font-medium text-gray-900">Canvas LMS</h4>
                                    <p class="text-xs text-gray-500">LMS • 987 usuarios</p>
                                </div>
                            </div>
                            <button onclick="installIntegration('canvas')" 
                                    class="px-3 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700">
                                Instalar
                            </button>
                        </div>

                        <!-- Zapier -->
                        <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-4">
                                    <span class="text-orange-600 font-bold">Z</span>
                                </div>
                                <div>
                                    <h4 class="text-sm font-medium text-gray-900">Zapier</h4>
                                    <p class="text-xs text-gray-500">Automatización • 2,567 usuarios</p>
                                </div>
                            </div>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Conectado
                            </span>
                        </div>

                        <!-- Google Analytics -->
                        <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center mr-4">
                                    <span class="text-yellow-600 font-bold">GA</span>
                                </div>
                                <div>
                                    <h4 class="text-sm font-medium text-gray-900">Google Analytics</h4>
                                    <p class="text-xs text-gray-500">Analítica • 1,789 usuarios</p>
                                </div>
                            </div>
                            <button onclick="installIntegration('analytics')" 
                                    class="px-3 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700">
                                Instalar
                            </button>
                        </div>

                        <!-- Salesforce -->
                        <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                                    <span class="text-blue-600 font-bold">SF</span>
                                </div>
                                <div>
                                    <h4 class="text-sm font-medium text-gray-900">Salesforce</h4>
                                    <p class="text-xs text-gray-500">CRM • 456 usuarios</p>
                                </div>
                            </div>
                            <button onclick="installIntegration('salesforce')" 
                                    class="px-3 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700">
                                Instalar
                            </button>
                        </div>

                        <!-- Single Sign-On -->
                        <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                                    <span class="text-purple-600 font-bold">SSO</span>
                                </div>
                                <div>
                                    <h4 class="text-sm font-medium text-gray-900">SAML/SSO</h4>
                                    <p class="text-xs text-gray-500">Autenticación • 3,421 usuarios</p>
                                </div>
                            </div>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Conectado
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Custom Integration CTA -->
            <div class="mt-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-8 text-white text-center">
                <h3 class="text-xl font-semibold mb-2">¿Necesitas una integración personalizada?</h3>
                <p class="mb-4 opacity-90">
                    Nuestro equipo puede ayudarte a crear integraciones específicas para tus necesidades.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <button onclick="contactSupport()" 
                            class="px-6 py-2 bg-white text-blue-600 rounded-md hover:bg-gray-100 font-medium">
                        Contactar Soporte
                    </button>
                    <button onclick="viewApiDocs()" 
                            class="px-6 py-2 border border-white text-white rounded-md hover:bg-white hover:text-blue-600 font-medium">
                        Ver Documentación API
                    </button>
                </div>
            </div>
        </main>
    </div>

    <!-- Integration Configuration Modal -->
    <div id="configModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <h3 class="text-lg font-medium text-gray-900 mb-4" id="modalTitle">Configurar Integración</h3>
                <div id="modalContent">
                    <!-- Content will be populated by JavaScript -->
                </div>
                <div class="flex justify-end space-x-3 pt-4">
                    <button onclick="closeModal()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                        Cancelar
                    </button>
                    <button onclick="saveIntegration()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        Guardar
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function installIntegration(integration) {
            alert(`Instalando integración: ${integration}`);
            // Simulate installation
            setTimeout(() => {
                alert(`¡Integración ${integration} instalada exitosamente!`);
            }, 1000);
        }

        function configureIntegration(integration) {
            document.getElementById('modalTitle').textContent = `Configurar ${integration}`;
            
            let content = '';
            switch(integration) {
                case 'moodle':
                    content = `
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">URL de Moodle</label>
                                <input type="url" value="https://moodle.universidad.edu" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Token de API</label>
                                <input type="password" value="••••••••••••••••" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                            </div>
                            <div>
                                <label class="flex items-center">
                                    <input type="checkbox" checked class="h-4 w-4 text-blue-600 rounded">
                                    <span class="ml-2 text-sm text-gray-700">Sincronización automática</span>
                                </label>
                            </div>
                        </div>
                    `;
                    break;
                default:
                    content = '<p class="text-gray-600">Configuración de integración...</p>';
            }
            
            document.getElementById('modalContent').innerHTML = content;
            document.getElementById('configModal').classList.remove('hidden');
        }

        function closeModal() {
            document.getElementById('configModal').classList.add('hidden');
        }

        function saveIntegration() {
            alert('Configuración guardada exitosamente');
            closeModal();
        }

        function contactSupport() {
            alert('Redirigiendo al formulario de contacto...');
        }

        function viewApiDocs() {
            window.location.href = '36_api_documentation.html';
        }
    </script>
</body>
</html>
