<!DOCTYPE html>
<html lang="es" class="h-full bg-gray-50">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generador de Preguntas IA - Arroyo University</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .progress-bar {
            background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
            animation: progress 3s ease-in-out;
        }
        @keyframes progress {
            0% { width: 0%; }
            100% { width: 75%; }
        }
    </style>
</head>
<body class="h-full">
    <div class="min-h-full">
        <!-- Navigation -->
        <nav class="bg-white shadow">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.84L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3z"/>
                            </svg>
                        </div>
                        <h1 class="ml-3 text-xl font-semibold text-gray-900">Universidad Ejemplo</h1>
                        <nav class="ml-8 flex space-x-4">
                            <a href="04_admin_tenant_dashboard.html" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">Dashboard</a>
                            <a href="08_course_creation.html" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">Cursos</a>
                            <a href="#" class="bg-blue-100 text-blue-700 px-3 py-2 rounded-md text-sm font-medium">Preguntas</a>
                            <a href="12_exam_creation_wizard.html" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">Exámenes</a>
                        </nav>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center space-x-2 text-sm">
                            <div class="w-2 h-2 bg-green-400 rounded-full"></div>
                            <span class="text-gray-600">IA: 2,450/5,000 tokens</span>
                        </div>
                        <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                            <span class="text-sm font-medium text-gray-700">MG</span>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="mb-8">
                <h2 class="text-2xl font-bold text-gray-900">Generador de Preguntas con IA</h2>
                <p class="mt-1 text-gray-600">Crea preguntas automáticamente usando inteligencia artificial</p>
            </div>

            <!-- Generator Form -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-8">
                    <form class="space-y-6">
                        <!-- Question Type Selection -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-4">
                                Tipo de pregunta
                            </label>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div class="relative">
                                    <input type="radio" name="question-type" value="writing" id="writing" class="sr-only" checked>
                                    <label for="writing" class="flex flex-col items-center p-6 border-2 border-blue-200 bg-blue-50 rounded-lg cursor-pointer hover:bg-blue-100 transition-colors">
                                        <svg class="w-8 h-8 text-blue-600 mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"/>
                                        </svg>
                                        <span class="text-sm font-medium text-blue-900">Writing</span>
                                        <span class="text-xs text-blue-700 text-center mt-1">Preguntas de escritura y composición</span>
                                    </label>
                                </div>
                                
                                <div class="relative">
                                    <input type="radio" name="question-type" value="listening" id="listening" class="sr-only">
                                    <label for="listening" class="flex flex-col items-center p-6 border-2 border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                                        <svg class="w-8 h-8 text-gray-600 mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z"/>
                                        </svg>
                                        <span class="text-sm font-medium text-gray-900">Listening</span>
                                        <span class="text-xs text-gray-600 text-center mt-1">Audio + preguntas de comprensión</span>
                                    </label>
                                </div>
                                
                                <div class="relative">
                                    <input type="radio" name="question-type" value="speaking" id="speaking" class="sr-only">
                                    <label for="speaking" class="flex flex-col items-center p-6 border-2 border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                                        <svg class="w-8 h-8 text-gray-600 mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"/>
                                        </svg>
                                        <span class="text-sm font-medium text-gray-900">Speaking</span>
                                        <span class="text-xs text-gray-600 text-center mt-1">Preguntas de expresión oral</span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Configuration -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="topic" class="block text-sm font-medium text-gray-700 mb-2">
                                    Tema o prompt
                                </label>
                                <textarea id="topic" rows="4" 
                                          placeholder="Ej: Hobbies y tiempo libre, actividades de fin de semana, deportes favoritos..."
                                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"></textarea>
                                <p class="mt-1 text-xs text-gray-500">Describe el tema o contexto para las preguntas</p>
                            </div>
                            
                            <div class="space-y-4">
                                <div>
                                    <label for="cefr-level" class="block text-sm font-medium text-gray-700 mb-2">
                                        Nivel CEFR
                                    </label>
                                    <select id="cefr-level" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option>A1 - Principiante</option>
                                        <option>A2 - Elemental</option>
                                        <option selected>B1 - Intermedio</option>
                                        <option>B2 - Intermedio Alto</option>
                                        <option>C1 - Avanzado</option>
                                        <option>C2 - Competencia</option>
                                    </select>
                                </div>
                                
                                <div>
                                    <label for="quantity" class="block text-sm font-medium text-gray-700 mb-2">
                                        Cantidad de preguntas: <span id="quantity-value" class="font-semibold text-blue-600">5</span>
                                    </label>
                                    <input type="range" id="quantity" min="1" max="20" value="5" 
                                           class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer">
                                    <div class="flex justify-between text-xs text-gray-500 mt-1">
                                        <span>1</span>
                                        <span>20</span>
                                    </div>
                                </div>
                                
                                <div>
                                    <label for="tone" class="block text-sm font-medium text-gray-700 mb-2">
                                        Tono y estilo
                                    </label>
                                    <select id="tone" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option>Formal académico</option>
                                        <option selected>Conversacional</option>
                                        <option>Profesional</option>
                                        <option>Casual</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Cost Estimation -->
                        <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-blue-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                                </svg>
                                <div class="flex-1">
                                    <p class="text-sm text-blue-800">
                                        <strong>Estimación:</strong> ~150 tokens IA • Tiempo: ~8 segundos
                                    </p>
                                    <p class="text-xs text-blue-600 mt-1">
                                        Quedarán 2,300 tokens después de esta generación
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- Generate Button -->
                        <div class="flex justify-end">
                            <button type="submit" id="generate-btn"
                                    class="px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 font-medium flex items-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                                </svg>
                                Generar Preguntas
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Progress Section (Hidden by default) -->
            <div id="progress-section" class="mt-8 bg-white shadow rounded-lg hidden">
                <div class="px-6 py-8">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-blue-600 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                            </svg>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Generando preguntas...</h3>
                        <p class="text-gray-600 mb-6">La IA está creando contenido personalizado para tu tema</p>
                        
                        <!-- Progress Bar -->
                        <div class="w-full bg-gray-200 rounded-full h-2 mb-4">
                            <div class="progress-bar h-2 rounded-full"></div>
                        </div>
                        
                        <!-- Progress Steps -->
                        <div class="space-y-2 text-sm text-gray-600">
                            <div class="flex items-center justify-center">
                                <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                                </svg>
                                <span>1/3: Analizando tema y contexto</span>
                            </div>
                            <div class="flex items-center justify-center">
                                <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                                </svg>
                                <span>2/3: Generando preguntas de escritura</span>
                            </div>
                            <div class="flex items-center justify-center text-blue-600">
                                <div class="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mr-2"></div>
                                <span>3/3: Aplicando formato y validación</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Results Section (Hidden by default) -->
            <div id="results-section" class="mt-8 bg-white shadow rounded-lg hidden">
                <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                    <h3 class="text-lg font-medium text-gray-900">Preguntas Generadas</h3>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50">
                            Regenerar
                        </button>
                        <button class="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700">
                            Guardar Todas
                        </button>
                    </div>
                </div>
                <div class="p-6">
                    <div class="space-y-6">
                        <!-- Sample Generated Question -->
                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex justify-between items-start mb-3">
                                <div class="flex items-center space-x-2">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        Writing
                                    </span>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        B1
                                    </span>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                        AI Generated
                                    </span>
                                </div>
                                <div class="flex space-x-2">
                                    <button class="text-gray-400 hover:text-gray-600">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"/>
                                        </svg>
                                    </button>
                                    <button class="text-gray-400 hover:text-red-600">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            <div class="prose max-w-none">
                                <p class="text-gray-900 font-medium mb-2">
                                    Describe tu hobby favorito y explica por qué te gusta tanto.
                                </p>
                                <p class="text-sm text-gray-600">
                                    En tu respuesta, incluye: qué actividad realizas, cuándo la practicas, con quién la compartes y qué beneficios te aporta. Escribe entre 100-150 palabras.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Slider value update
        document.getElementById('quantity').addEventListener('input', function(e) {
            document.getElementById('quantity-value').textContent = e.target.value;
        });

        // Form submission simulation
        document.getElementById('generate-btn').addEventListener('click', function(e) {
            e.preventDefault();
            
            // Hide form, show progress
            document.querySelector('.bg-white.shadow.rounded-lg').style.display = 'none';
            document.getElementById('progress-section').classList.remove('hidden');
            
            // Simulate progress completion
            setTimeout(() => {
                document.getElementById('progress-section').classList.add('hidden');
                document.getElementById('results-section').classList.remove('hidden');
            }, 8000);
        });
    </script>
</body>
</html>
