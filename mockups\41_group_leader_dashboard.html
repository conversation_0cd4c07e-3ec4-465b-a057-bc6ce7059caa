<!DOCTYPE html>
<html lang="es" class="h-full bg-gray-50">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Panel de Líder de Grupo - Arroyo University</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
    </style>
</head>
<body class="h-full">
    <div class="min-h-full">
        <!-- Navigation -->
        <nav class="bg-white shadow">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <button onclick="window.location.href='05_content_creator_dashboard.html'" 
                                class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center hover:bg-blue-700 transition-colors">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.84L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3z"/>
                            </svg>
                        </button>
                        <h1 class="ml-3 text-xl font-semibold text-gray-900">Universidad Ejemplo</h1>
                        <nav class="ml-8 flex space-x-4">
                            <a href="05_content_creator_dashboard.html" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">Dashboard</a>
                            <a href="39_course_marketplace.html" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">Marketplace</a>
                            <a href="#" class="bg-blue-100 text-blue-700 px-3 py-2 rounded-md text-sm font-medium">Mis Grupos</a>
                        </nav>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                            <span class="text-sm font-medium text-gray-700">IL</span>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="mb-8">
                <h2 class="text-2xl font-bold text-gray-900">Panel de Líder de Grupo</h2>
                <p class="mt-1 text-gray-600">Gestiona tus grupos y miembros</p>
            </div>

            <!-- My Groups -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- DevOps Advanced Group -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-lg font-medium text-gray-900">DevOps Avanzado</h3>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                        Privado
                                    </span>
                                </div>
                            </div>
                            <div class="text-sm text-gray-500">
                                12 miembros
                            </div>
                        </div>
                    </div>
                    
                    <div class="p-6">
                        <p class="text-gray-600 text-sm mb-4">
                            Grupo privado para expertos en DevOps. Discusiones avanzadas sobre CI/CD, Kubernetes y arquitecturas cloud.
                        </p>
                        
                        <!-- Quick Actions -->
                        <div class="grid grid-cols-2 gap-3 mb-6">
                            <button onclick="openInviteModal('devops-advanced')" 
                                    class="flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                                </svg>
                                Invitar Miembro
                            </button>
                            <button onclick="viewMembers('devops-advanced')" 
                                    class="flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                                </svg>
                                Ver Miembros
                            </button>
                        </div>
                        
                        <!-- Recent Activity -->
                        <div>
                            <h4 class="text-sm font-medium text-gray-900 mb-3">Actividad Reciente</h4>
                            <div class="space-y-2">
                                <div class="flex items-center text-sm text-gray-600">
                                    <div class="w-2 h-2 bg-green-400 rounded-full mr-3"></div>
                                    <span>Carlos López se unió al grupo</span>
                                    <span class="ml-auto text-xs text-gray-400">Hace 2 días</span>
                                </div>
                                <div class="flex items-center text-sm text-gray-600">
                                    <div class="w-2 h-2 bg-blue-400 rounded-full mr-3"></div>
                                    <span>Nueva discusión: "Kubernetes vs Docker Swarm"</span>
                                    <span class="ml-auto text-xs text-gray-400">Hace 3 días</span>
                                </div>
                                <div class="flex items-center text-sm text-gray-600">
                                    <div class="w-2 h-2 bg-purple-400 rounded-full mr-3"></div>
                                    <span>Ana Martínez compartió un recurso</span>
                                    <span class="ml-auto text-xs text-gray-400">Hace 5 días</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Cloud Architecture Group -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-lg font-medium text-gray-900">Cloud Architecture</h3>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Público
                                    </span>
                                </div>
                            </div>
                            <div class="text-sm text-gray-500">
                                28 miembros
                            </div>
                        </div>
                    </div>
                    
                    <div class="p-6">
                        <p class="text-gray-600 text-sm mb-4">
                            Comunidad para arquitectos cloud. Discusiones sobre AWS, Azure, GCP y mejores prácticas de arquitectura.
                        </p>
                        
                        <!-- Quick Actions -->
                        <div class="grid grid-cols-2 gap-3 mb-6">
                            <button onclick="manageGroup('cloud-architecture')" 
                                    class="flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                </svg>
                                Configurar
                            </button>
                            <button onclick="viewMembers('cloud-architecture')" 
                                    class="flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                                </svg>
                                Ver Miembros
                            </button>
                        </div>
                        
                        <!-- Recent Activity -->
                        <div>
                            <h4 class="text-sm font-medium text-gray-900 mb-3">Actividad Reciente</h4>
                            <div class="space-y-2">
                                <div class="flex items-center text-sm text-gray-600">
                                    <div class="w-2 h-2 bg-green-400 rounded-full mr-3"></div>
                                    <span>3 nuevos miembros se unieron</span>
                                    <span class="ml-auto text-xs text-gray-400">Hoy</span>
                                </div>
                                <div class="flex items-center text-sm text-gray-600">
                                    <div class="w-2 h-2 bg-blue-400 rounded-full mr-3"></div>
                                    <span>Discusión: "Serverless vs Containers"</span>
                                    <span class="ml-auto text-xs text-gray-400">Ayer</span>
                                </div>
                                <div class="flex items-center text-sm text-gray-600">
                                    <div class="w-2 h-2 bg-yellow-400 rounded-full mr-3"></div>
                                    <span>Evento programado: "AWS Workshop"</span>
                                    <span class="ml-auto text-xs text-gray-400">Hace 2 días</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pending Invitations -->
            <div class="mt-8 bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Invitaciones Pendientes</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                    <span class="text-xs font-medium text-blue-600">MR</span>
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-gray-900">María Rodríguez</div>
                                    <div class="text-xs text-gray-500">Invitada a: DevOps Avanzado</div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="text-xs text-gray-500">Enviada hace 2 días</span>
                                <button onclick="resendInvitation('maria-rodriguez')" 
                                        class="text-blue-600 hover:text-blue-800 text-xs">
                                    Reenviar
                                </button>
                            </div>
                        </div>
                        
                        <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                    <span class="text-xs font-medium text-green-600">JS</span>
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-gray-900">Juan Silva</div>
                                    <div class="text-xs text-gray-500">Invitado a: DevOps Avanzado</div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="text-xs text-gray-500">Enviada hace 5 días</span>
                                <button onclick="resendInvitation('juan-silva')" 
                                        class="text-blue-600 hover:text-blue-800 text-xs">
                                    Reenviar
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Invite Member Modal -->
    <div id="inviteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Invitar Miembro al Grupo</h3>
                <form class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Email del Usuario *</label>
                        <input type="email" id="inviteEmail" required 
                               placeholder="<EMAIL>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Mensaje Personal (Opcional)</label>
                        <textarea id="inviteMessage" rows="3" 
                                  placeholder="Mensaje de bienvenida para el nuevo miembro..."
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                    </div>
                    
                    <div class="flex justify-end space-x-3 pt-4">
                        <button type="button" onclick="closeInviteModal()" 
                                class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                            Cancelar
                        </button>
                        <button type="button" onclick="sendInvitation()" 
                                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                            Enviar Invitación
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        let currentGroupId = '';

        function openInviteModal(groupId) {
            currentGroupId = groupId;
            document.getElementById('inviteModal').classList.remove('hidden');
        }

        function closeInviteModal() {
            document.getElementById('inviteModal').classList.add('hidden');
            document.getElementById('inviteEmail').value = '';
            document.getElementById('inviteMessage').value = '';
        }

        function sendInvitation() {
            const email = document.getElementById('inviteEmail').value;
            const message = document.getElementById('inviteMessage').value;
            
            if (!email) {
                alert('Por favor, ingresa un email válido');
                return;
            }
            
            alert(`¡Invitación enviada exitosamente!\n\nEmail: ${email}\nGrupo: ${currentGroupId}\n\nEl usuario recibirá un email con la invitación para unirse al grupo privado.`);
            closeInviteModal();
        }

        function viewMembers(groupId) {
            alert(`Viendo miembros del grupo: ${groupId}`);
        }

        function manageGroup(groupId) {
            alert(`Configurando grupo: ${groupId}`);
        }

        function resendInvitation(userId) {
            alert(`Reenviando invitación a: ${userId}`);
        }
    </script>
</body>
</html>
