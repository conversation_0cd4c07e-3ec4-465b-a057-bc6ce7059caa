# Arroyo University – Plataforma Educativa Multi-Tenant Impulsada por IA

## Introducción

Arroyo University representa una solución integral de plataforma educativa SaaS multi-tenant diseñada para transformar la gestión y entrega de educación digital. Este documento define los requisitos técnicos y funcionales para el desarrollo de una plataforma escalable, segura y automatizada que aprovecha la Inteligencia Artificial generativa para optimizar procesos educativos, evaluaciones y experiencias de aprendizaje personalizadas.

La plataforma está diseñada para organizaciones que buscan modernizar sus procesos educativos mediante automatización inteligente, cumpliendo con los más altos estándares internacionales de seguridad, privacidad y accesibilidad, incluyendo IEEE 830, OWASP, GDPR/CCPA, FERPA, WCAG 2.1 y prácticas DevSecOps.

---

## Tabla de Contenidos

1. [<PERSON><PERSON><PERSON><PERSON>, Visión y Contexto Estratégico](#1-propósito-visión-y-contexto-estratégico)
   - 1.1 [Declaración de Propósito](#11-declaración-de-propósito)
   - 1.2 [Meta Estratégica](#12-meta-estratégica)

2. [Alcance, Usuarios y Principios Operativos](#2-alcance-usuarios-y-principios-operativos)
   - 2.1 [Alcance Funcional](#21-alcance-funcional)
   - 2.2 [Tipos de Usuario y Roles](#22-tipos-de-usuario-y-roles)

3. [Arquitectura Técnica y Operativa](#3-arquitectura-técnica-y-operativa)
   - 3.1 [Topología General](#31-topología-general)
   - 3.2 [Frontend SPA](#32-frontend-spa)
   - 3.3 [Backend Core API](#33-backend-core-api)
   - 3.4 [Servicios de IA](#34-servicios-de-ia)
   - 3.5 [Base de Datos](#35-base-de-datos)
   - 3.6 [Jobs, Storage y Observabilidad](#36-jobs-storage-y-observabilidad)
   - 3.7 [Infraestructura](#37-infraestructura)

4. [Modelo de Datos Relacional y Extensible](#4-modelo-de-datos-relacional-y-extensible)
   - 4.1 [Entidades Fundamentales](#41-entidades-fundamentales)
   - 4.2 [Ejemplo de Modelo Relacional](#42-ejemplo-de-modelo-relacional)
   - 4.3 [Optimizaciones y Seguridad de Datos](#43-optimizaciones-y-seguridad-de-datos)

5. [Arquitectura de API y Estándares de Integración](#5-arquitectura-de-api-y-estándares-de-integración)
   - 5.1 [Organización de Endpoints](#51-organización-de-endpoints)
   - 5.2 [Convenciones y Prácticas](#52-convenciones-y-prácticas)

6. [Flujos y Orquestaciones Clave](#6-flujos-y-orquestaciones-clave)
   - 6.1 [Alta de Tenant y Onboarding](#61-alta-de-tenant-y-onboarding)
   - 6.2 [Generación y Corrección de Preguntas/Exámenes por IA](#62-generación-y-corrección-de-preguntasexámenes-por-ia)
   - 6.3 [Intentos de Examen y Entrega](#63-intentos-de-examen-y-entrega)
   - 6.4 [Backups, Restauración y Disaster Recovery](#64-backups-restauración-y-disaster-recovery)
   - 6.5 [Analítica, Reporting y Métricas](#65-analítica-reporting-y-métricas)

7. [Requisitos No Funcionales y KPIs](#7-requisitos-no-funcionales-y-kpis)
   - 7.1 [Seguridad y Compliance](#71-seguridad-y-compliance)
   - 7.2 [Escalabilidad y Desempeño](#72-escalabilidad-y-desempeño)
   - 7.3 [Observabilidad y Recuperación](#73-observabilidad-y-recuperación)
   - 7.4 [Accesibilidad y Usabilidad](#74-accesibilidad-y-usabilidad)
   - 7.5 [Mantenibilidad y Calidad](#75-mantenibilidad-y-calidad)

8. [Cumplimiento de Estándares y Mejores Prácticas](#8-cumplimiento-de-estándares-y-mejores-prácticas)

9. [Futuro, Extensibilidad y Marketplace](#9-futuro-extensibilidad-y-marketplace)

10. [Métricas de Éxito y Validación](#10-métricas-de-éxito-y-validación)

11. [Requisitos Funcionales Detallados](#11-requisitos-funcionales-detallados)
    - 11.1 [Gestión de Tenants y Configuración](#111-gestión-de-tenants-y-configuración)
    - 11.2 [Gestión de Usuarios, Autenticación y Seguridad](#112-gestión-de-usuarios-autenticación-y-seguridad)
    - 11.3 [Gestión de Roles, Permisos y Grupos (RBAC)](#113-gestión-de-roles-permisos-y-grupos-rbac)
    - 11.4 [Cursos, Grupos y Contenido Educativo](#114-cursos-grupos-y-contenido-educativo)
    - 11.5 [Banco de Preguntas y Evaluación con IA](#115-banco-de-preguntas-y-evaluación-con-ia)
    - 11.6 [Exámenes, Intentos y Corrección](#116-exámenes-intentos-y-corrección)
    - 11.7 [Analítica, Reportes y Dashboards](#117-analítica-reportes-y-dashboards)
    - 11.8 [Notificaciones, Webhooks e Integraciones](#118-notificaciones-webhooks-e-integraciones)
    - 11.9 [Backups, Recuperación y Auditoría](#119-backups-recuperación-y-auditoría)
    - 11.10 [Facturación y Planes de Suscripción](#1110-facturación-y-planes-de-suscripción)
    - 11.11 [Cumplimiento, Accesibilidad e Internacionalización](#1111-cumplimiento-accesibilidad-e-internacionalización)
    - 11.12 [Soporte Mobile/PWA y Experiencia Offline](#1112-soporte-mobilepwa-y-experiencia-offline)
    - 11.13 [Extensibilidad y Plugins](#1113-extensibilidad-y-plugins)

12. [Flujos Clave Detallados](#12-flujos-clave-detallados)
    - 12.1 [Alta de Tenant y Aprovisionamiento](#121-alta-de-tenant-y-aprovisionamiento)
    - 12.2 [Generación de Preguntas IA](#122-generación-de-preguntas-ia)
    - 12.3 [Creación y Publicación de Examen](#123-creación-y-publicación-de-examen)
    - 12.4 [Intento de Examen y Entrega](#124-intento-de-examen-y-entrega)
    - 12.5 [Corrección Manual (Override)](#125-corrección-manual-override)
    - 12.6 [Pipeline de Dashboards y Métricas](#126-pipeline-de-dashboards-y-métricas)
    - 12.7 [Upgrade/Downgrade de Plan de Suscripción](#127-upgradedowngrade-de-plan-de-suscripción)
    - 12.8 [Backup y Restore](#128-backup-y-restore)
    - 12.9 [Flujos Mobile/PWA Offline](#129-flujos-mobilepwa-offline)

13. [Índice de API](#13-índice-de-api)
    - 13.1 [Autenticación y Seguridad](#131-autenticación-y-seguridad)
    - 13.2 [Gestión de Tenants](#132-gestión-de-tenants)
    - 13.3 [Usuarios y Roles](#133-usuarios-y-roles)
    - 13.4 [Grupos y Matrículas](#134-grupos-y-matrículas)
    - 13.5 [Cursos y Contenido](#135-cursos-y-contenido)
    - 13.6 [Exámenes y Preguntas](#136-exámenes-y-preguntas)
    - 13.7 [IA y Procesamiento Avanzado](#137-ia-y-procesamiento-avanzado)
    - 13.8 [Intentos y Resultados de Examen](#138-intentos-y-resultados-de-examen)
    - 13.9 [Notificaciones y Comunicación](#139-notificaciones-y-comunicación)
    - 13.10 [Dashboards y Métricas](#1310-dashboards-y-métricas)
    - 13.11 [Administración, Backup y Suscripciones](#1311-administración-backup-y-suscripciones)
    - 13.12 [Integraciones y Webhooks](#1312-integraciones-y-webhooks)
    - 13.13 [Utilidades y Configuración](#1313-utilidades-y-configuración)
    - 13.14 [Logs y Auditoría](#1314-logs-y-auditoría)

14. [Esquema de Base de Datos](#14-esquema-de-base-de-datos)

15. [Anexo Técnico Complementario](#15-anexo-técnico-complementario)
    - 15.1 [Reglas y Constraints Avanzados](#151-reglas-y-constraints-avanzados)
    - 15.2 [Estructuras y Relaciones Faltantes](#152-estructuras-y-relaciones-faltantes)
    - 15.3 [Criterios Técnicos de Aceptación Avanzados](#153-criterios-técnicos-de-aceptación-avanzados)

---

## 1. Propósito, Visión y Contexto Estratégico

### 1.1 Declaración de Propósito
Arroyo University tiene como propósito transformar la gestión y entrega de educación digital para organizaciones a través de una plataforma SaaS multi-tenant, completamente automatizada, segura y escalable, capaz de orquestar cursos, exámenes y procesos de aprendizaje personalizados mediante Inteligencia Artificial (IA) generativa y analítica avanzada, garantizando reducción de costos, mejor experiencia de usuario y cumplimiento de los más altos estándares internacionales en seguridad, privacidad y accesibilidad.

### 1.2 Meta Estratégica
- **Automatizar** en al menos 80% los procesos de evaluación y retroalimentación educativa.
- **Facilitar** la detección y retención de talento con analítica predictiva y rutas de aprendizaje personalizadas.
- **Escalar** la solución para soportar miles de tenants y decenas de miles de usuarios concurrentes con mínima administración.
- **Ofrecer** una experiencia UX/UI de primer nivel, accesible, inclusiva, multilingüe y omnicanal.
- **Cumplir** con normativas y buenas prácticas internacionales: IEEE 830, OWASP, GDPR/CCPA, FERPA, WCAG 2.1, DevSecOps y RESTful.

---

## 2. Alcance, Usuarios y Principios Operativos

### 2.1 Alcance Funcional

**Iteración Inicial (MVP)**
- CRUD de cursos, módulos y materiales multimedia.
- Creación y gestión de exámenes, banco de preguntas, intentos, scoring automático/manual, detección de plagio y moderación de contenido ofensivo vía IA.
- Gestión multi-tenant (aislamiento estricto por tenant, administración por rol, configuraciones por tenant).
- Notificaciones, dashboards y analítica básica.
- Seguridad avanzada: autenticación JWT/OAuth2, MFA, control de permisos granular, logs de auditoría, backups y recuperación.

**Roadmap (12-24 meses)**
- Analítica predictiva de talento, marketplace de cursos y extensiones (plugin system), integraciones externas (LMS, HRIS, SSO), workflows automáticos (onboarding, backup/restore, triggers).
- Internacionalización total, experiencia mobile/PWA, API pública, analítica avanzada, soporte omnicanal, sistema de extensiones vía WebAssembly.

### 2.2 Tipos de Usuario y Roles

- **SysAdmin (plataforma):** Gestión global, onboarding de tenants, auditoría, gestión de backups y restauraciones, control de compliance.
- **Admin Tenant:** Administración local de usuarios, cursos, exámenes, configuraciones y métricas de su tenant.
- **Instructor/Content Creator:** Creación y edición de cursos, preguntas, exámenes y revisión manual de resultados.
- **Estudiante/Usuario final:** Acceso a cursos, presentación de exámenes, seguimiento de progreso, feedback inmediato.
- **Usuarios temporales:** Acceso restringido por invitación con expiración controlada.

---

## 3. Arquitectura Técnica y Operativa

### 3.1 Topología General

### 3.2 Frontend SPA
- Tecnologías: React 18+ o Vue 3 (Composition API), Tailwind CSS v4, i18next.
- Funcionalidades: Responsive, PWA, soporte offline vía IndexedDB, accesibilidad (WCAG 2.1 AA), dark/light mode, dashboards personalizados, carga multimedia, audio/video nativo, internacionalización completa, navegación por roles, atajos, ayuda contextual.

### 3.3 Backend Core API
- Framework: FastAPI (Python 3.11+), SQLAlchemy ORM, Alembic para migraciones, patrones Clean Architecture.
- Funciones: API RESTful versionada (`/api/v1/`), lógica de negocio, RBAC granular, gestión multi-tenant, auditoría, rate limiting, triggers y jobs asíncronos, integración OAuth2/JWT, validación Pydantic.
- Seguridad: JWT firmado, MFA, hashing de contraseñas con bcrypt, validación de entrada/salida, CORS estricto, escaneo automático de secretos y dependencias, protección CSRF y XSS.

### 3.4 Servicios de IA
- Arquitectura microservicios: FastAPI, Celery + Redis/Kafka para tareas asíncronas.
- Funcionalidades: Generación/corrección de preguntas, scoring de respuestas (GPT-4, modelos custom), moderación de contenido, detección de plagio (integración Turnitin/otros), síntesis/recognición de voz (Azure Speech).
- Seguridad y compliance: M-TLS interno, jobs rastreables, logs de uso/coste por tenant.

### 3.5 Base de Datos
- Motor: PostgreSQL 13+ (citext, pgvector, jsonb), estrategias RLS (Row-Level Security), backups automáticos, particionamiento y sharding opcional.
- Modelo: Shared DB, Shared Schema, todas las tablas con `tenant_id`, claves foráneas con restricciones ON DELETE CASCADE, integridad referencial y triggers para logging.

### 3.6 Jobs, Storage y Observabilidad
- Jobs: Redis/Celery, Kafka (eventos críticos, escalar a futuro), pipelines de IA y analítica.
- Storage: Azure Blob/AWS S3, CDN, URLs presignadas, gestión de versiones y políticas de retención.
- Observabilidad: Prometheus, Grafana, Loki, Sentry, OpenTelemetry, MTTR < 30 min.

### 3.7 Infraestructura
- Contenerización: Docker, Docker Compose (dev), AKS/EKS para producción, despliegues canary/blue-green, IaC (Terraform), CI/CD automatizado (GitHub Actions).

---

## 4. Modelo de Datos Relacional y Extensible

### 4.1 Entidades Fundamentales

- **Tenants:** Aislamiento SaaS, configuración, planes, auditoría, facturación, uso de IA.
- **Users:** Cuentas, roles, MFA, actividad, grupos, preferencias, auditoría.
- **Roles/Permissions/Groups:** RBAC editable, permisos temporales, asignación por grupo, checklist UX estilo Discord, versionado.
- **Courses/Modules/Items:** CRUD, publicación/archivado, multimedios, versionado, asignación por grupo, relación con exámenes.
- **Exams/TestItems:** Configuración flexible, banco de preguntas, tags, dificultad, branching lógico, versionado, auditoría, preguntas condicionales, soporte para multimedia (audio/video/imágenes/código).
- **UserExamAttempts/Answers:** Tracking de intentos, estado, tiempo, scoring AI/manual, feedback, métricas CEFR, plagio.
- **Analytics/Metrics/Dashboards:** KPIs de cursos/exámenes, uso de IA, reportes por grupo, CSV/PDF export, integración con PowerBI.
- **Notifications/Webhooks:** Notificaciones push/email, historial, integración con sistemas externos vía Webhooks (HMAC).
- **AuditLogs/Backups:** Logs por entidad y acción, backups incrementales diarios, restauración selectiva.

### 4.2 Ejemplo de Modelo Relacional

Incluye todas las tablas y relaciones recomendadas:
- Relaciones N:M explícitas (link_user_roles, link_role_permissions, link_user_groups, link_group_courses, courses_exams_items).
- Todos los recursos ligados a `tenant_id` para aislamiento seguro.
- Soporte para extensiones (campos `metadata` tipo JSONB para personalización sin romper la estructura principal).

### 4.3 Optimizaciones y Seguridad de Datos

- Índices compuestos por tenant_id y campo buscado.
- Row-level security habilitado por defecto.
- Passwords con bcrypt, MFA, logs de acceso, validaciones estrictas de integridad.
- Backups automáticos, retención de 30 días, recuperación granular (RTO 4h, RPO 24h).
- Encriptación en tránsito (SSL/TLS), configuración de cifrado at-rest.

---

## 5. Arquitectura de API y Estándares de Integración

### 5.1 Organización de Endpoints

- `/auth/` (login/logout, JWT, MFA, refresh, SSO)
- `/tenants/` (alta/baja/configuración/plan, backups, restores)
- `/users/` (CRUD, roles, MFA, invitaciones, grupos, logs)
- `/roles/` y `/permissions/` (gestión RBAC editable, auditoría, versionado, checklist UX)
- `/groups/` (equipos, membresías, asignación cursos)
- `/courses/` y `/course-items/` (gestión cursos, multimedia, publicación, versionado)
- `/exams/` y `/test-items/` (exámenes, preguntas, tags, generación/corrección IA, versionado, plagio)
- `/enrollment/` (matrícula, tracking de progreso)
- `/ai/` (generación/corrección preguntas/exámenes, scoring, moderación, jobs asíncronos, feedback, reporte de coste IA)
- `/dashboard/` y `/metrics/` (KPIs, dashboards filtrables, reporting, exportación)
- `/notifications/`, `/webhooks/`, `/audit-logs/`, `/backups/` (comunicación, integración, auditoría y resiliencia)

### 5.2 Convenciones y Prácticas

- Versionado de API por path (`/api/v1/`).
- Naming RESTful: recursos en plural, snake_case para campos.
- JSON como formato estándar, paginación y filtrado configurable.
- Rate limiting por endpoint y tenant, headers estándar.
- Documentación automática OpenAPI/Swagger + Redoc, ejemplos de requests/responses, contratos claros de error (RFC 7807).
- Validación exhaustiva de requests/responses con Pydantic.
- Tests unitarios/integración/seguridad con pytest, httpx, factory_boy, coverage.

---

## 6. Flujos y Orquestaciones Clave

### 6.1 Alta de Tenant y Onboarding
- Backend: `/tenants` crea nuevo tenant, dispara pipelines Terraform, prepara storage, onboarding via email, aprovisionamiento instantáneo.
- Frontend: Wizard guiado para configuración inicial, branding, dominios, roles, preferencias.
- Auditoría: Cada paso registrado, logs de actividad y errores.

### 6.2 Generación y Corrección de Preguntas/Exámenes por IA
- Backend: `/ai/generate` recibe prompts/temas, genera preguntas y scripts, sintetiza audio si es necesario, almacena resultados y estado en jobs.
- Frontend: UI intuitiva para seleccionar tipo, tema, cantidad, vista previa y edición rápida.
- Asincronía: Notificaciones automáticas de finalización vía websockets, retry/alertas en caso de fallos.

### 6.3 Intentos de Examen y Entrega
- Backend: `/exam-attempts` inicia/continúa intento, `/answers` para respuestas, scoring automático/manual, tracking de tiempo y eventos (copy/paste, cambio de ventana), cierre automático por timeout o desconexión.
- Frontend: Temporizadores visibles, autosave, UX offline resiliente, feedback inmediato al terminar.

### 6.4 Backups, Restauración y Disaster Recovery
- Cron: Backups automáticos, retención configurable, soporte incremental.
- Backend: Endpoints de restauración selectiva, validaciones y simulacros regulares de DR.
- Auditoría: Registro de cada operación crítica.

### 6.5 Analítica, Reporting y Métricas
- Backend: KPIs agregados, dashboards dinámicos, exportación CSV/PDF, integración con BI.
- Frontend: Filtros avanzados, visualizaciones claras, drill-down por curso, usuario, grupo o tenant.

---

## 7. Requisitos No Funcionales y KPIs

### 7.1 Seguridad y Compliance
- OWASP Top 10, CIS Benchmarks, GDPR/FERPA/CCPA, control estricto de acceso, encriptación en tránsito y en reposo, logs de auditoría completos.
- Detección y respuesta automática a anomalías de seguridad y eventos críticos.
- Soporte para solicitudes DSR (Data Subject Rights), retención y borrado seguro.

### 7.2 Escalabilidad y Desempeño
- SLA: p95 < 200 ms en CRUD, latencia IA end-to-end < 15s.
- Concurrencia: 10,000+ usuarios activos sin degradación.
- HPA, sharding por tenant, pooling, particionamiento, réplicas de lectura.
- Optimización continua de queries, uso de pgvector para búsquedas semánticas rápidas.

### 7.3 Observabilidad y Recuperación
- Logs 100% centralizados, trazas distribuidas, monitoreo de métricas por tenant y globales.
- MTTR < 30 min para incidentes críticos.
- Simulacros semestrales de disaster recovery (RPO 24h, RTO 4h).

### 7.4 Accesibilidad y Usabilidad
- Cumplimiento estricto WCAG 2.1 AA, UX adaptativo, mobile-first, onboarding y tutoriales, ayuda contextual, feedback inmediato y navegación por atajos.

### 7.5 Mantenibilidad y Calidad
- Cobertura de pruebas automatizadas ≥ 80% código crítico, CI/CD completo, documentación viva (ADR, OpenAPI, diagrams), migraciones reversibles, arquitectura modular y extensible.

---

## 8. Cumplimiento de Estándares y Mejores Prácticas

- **IEEE 830:** Todas las secciones obligatorias cubiertas, trazabilidad de requisitos, estructura clara y verificable.
- **RESTful API:** Recursos bien definidos, versionado, errores estructurados, contratos y documentación exhaustiva.
- **DevSecOps:** Integración continua, despliegues automáticos, monitoreo de seguridad, escaneo de dependencias.
- **Accesibilidad, privacidad y legalidad:** WCAG 2.1, GDPR, FERPA, términos de servicio, consentimiento parental, logs de cambios en registros académicos.
- **Internacionalización:** Strings y formatos totalmente externalizados, soporte para múltiples idiomas, ajuste dinámico por usuario y tenant.

---

## 9. Futuro, Extensibilidad y Marketplace

- **Plugin system basado en WebAssembly:** Extensiones dinámicas sin downtime, posibilidad de integrar lógica de negocio, validaciones o nuevas experiencias educativas.
- **Marketplace colaborativo:** Distribución y compra de cursos, exámenes, plugins, dashboards, analítica avanzada entre tenants y creadores externos.
- **APIs públicas:** Scopes granulares por tenant/usuario, integración segura con sistemas externos (LMS, HRIS, bots, asistentes).
- **Soporte mobile nativo y omnicanal:** Apps nativas y/o PWA con capacidades avanzadas, integración con mensajería y videoconferencia.

---

## 10. Métricas de Éxito y Validación

- **Net Promoter Score (NPS):** Objetivo ≥ 40 en tenants reales tras el primer mes de uso.
- **Reducción medible en tiempos/costos de evaluación:** ≥ 80% frente a procesos manuales tradicionales.
- **Tasa de retención de usuarios y creadores:** ≥ 70% a 6 meses.
- **SLA operativos:** ≥ 99.5% uptime, ningún CVE crítico sin resolver > 30 días.
- **Cobertura de pruebas:** ≥ 80% en componentes críticos y flujos de negocio.

---

## 11. Requisitos Funcionales Detallados

A continuación se presentan las Historias de Usuario (HU) y Criterios de Aceptación (CA) clave para **Arroyo University**, unificando el formato ágil, la trazabilidad y los detalles requeridos por la visión original y las mejores prácticas de la ingeniería de software moderna:

---

### 11.1 Gestión de Tenants y Configuración

- **HU-01 Alta y configuración de tenant**
  - Como SysAdmin, quiero crear un tenant con onboarding guiado para aprovisionamiento inmediato y configuración inicial, para que nuevos clientes puedan comenzar en minutos.
  - **CA:** La URL `{tenant}.arroyo.app` responde `200 OK` < 1s tras creación; backup inicial registrado; wizard de onboarding completado.

- **HU-02 Configuración y parámetros globales por tenant**
  - Como Admin Tenant, quiero definir configuraciones específicas (branding, cuotas, idioma, integraciones), para personalizar la experiencia y control operativo.
  - **CA:** Configuración vía UI y API, cambios auditados y reflejados en tiempo real, rollback disponible.

---

### 11.2 Gestión de Usuarios, Autenticación y Seguridad

- **HU-03 Registro, invitación y CRUD de usuarios**
  - Como Admin Tenant, quiero registrar, invitar o editar usuarios (incluyendo usuarios temporales), para gestionar el acceso de mi organización.
  - **CA:** Invitaciones con expiración configurable; solo usuarios `verified` pueden iniciar sesión; logs de cambios por usuario.

- **HU-04 Autenticación multifactor y recuperación**
  - Como usuario, quiero poder activar MFA y gestionar códigos de recuperación, para aumentar la seguridad de mi cuenta.
  - **CA:** Flujo TOTP según RFC 6238, opción de enforcing por política; recuperación solo con doble factor; logs de intentos.

- **HU-05 Soporte de SSO empresarial**
  - Como Tenant Admin, quiero poder integrar mi proveedor SAML/OIDC para login único, para facilitar onboarding y compliance.
  - **CA:** Mapeo de grupos a roles interno; fallback a login local solo para SysAdmin.

---

### 10.3 Gestión de Roles, Permisos y Grupos (RBAC)

- **HU-06 Gestión editable de roles y permisos**
  - Como Admin Tenant, quiero crear, editar y clonar roles y permisos, usando un checklist UX rápido (estilo Discord), para delegar capacidades de forma granular.
  - **CA:** Todos los permisos listados, cambios en tiempo real, versionado de roles y auditoría de diffs.

- **HU-07 Asignación y expiración de roles/grupos**
  - Como Admin Tenant, quiero asignar roles y permisos temporales, y administrar grupos/equipos para delegar acceso controlado y segmentado.
  - **CA:** Permisos activos según `active_from` / `expires_at`; revocación automática por cron.

---

### 10.4 Cursos, Grupos y Contenido Educativo

- **HU-08 CRUD y publicación/archivado de cursos y módulos**
  - Como Instructor, quiero crear, clonar y publicar cursos/módulos multimedia, para entregar rutas de aprendizaje estructuradas.
  - **CA:** Estado DRAFT/PUBLISHED/ARCHIVED, soporta multimedia (PDF, video, audio), checksum de integridad, control de versiones.

- **HU-09 Asignación de cursos a grupos y gestión de inscripción**
  - Como Admin Tenant, quiero asignar cursos a grupos, y permitir diferentes métodos de inscripción (auto, invitación, aprobación), para segmentar el aprendizaje.
  - **CA:** Tracking de inscripciones, dashboard de progreso y alertas automáticas por avance.

---

### 10.5 Banco de Preguntas y Evaluación con IA

- **HU-10 Creación y edición de preguntas manuales y automáticas**
  - Como Creador de Contenido, quiero crear, importar o generar (por IA) preguntas de tipo variado (writing, listening, speaking, code, imagen), para construir exámenes versátiles y personalizados.
  - **CA:** Prompts IA con skill/CEFR/tono, importación/exportación CSV/QTI, edición con control de versiones.

- **HU-11 Moderación y detección de plagio**
  - Como Admin Tenant, quiero que el sistema detecte automáticamente contenido ofensivo y plagio, para mantener la integridad y cumplimiento.
  - **CA:** Contenido ofensivo flaggeado y notificado; integración con Turnitin u otro, reporte automático por porcentaje.

---

### 10.6 Exámenes, Intentos y Corrección

- **HU-12 Creación, asignación y versionado de exámenes**
  - Como Instructor, quiero crear, asignar y versionar exámenes, configurando temporizadores globales o por pregunta y lógica de branching.
  - **CA:** Examen DRAFT/PUBLISHED, tiempo configurable, lógica condicional preparada.

- **HU-13 Intento de examen y autosave**
  - Como Estudiante, quiero presentar exámenes con autosave y opción de reanudación tras desconexión, para evitar pérdida de avances.
  - **CA:** Un solo intento `IN_PROGRESS` por usuario, autosave cada 10s, cierre automático por inactividad.

- **HU-14 Corrección automática/manual y feedback inmediato**
  - Como Instructor/IA, quiero que las respuestas sean corregidas automáticamente (MCQ, open answer por IA), pero pueda overridear resultados, para combinar eficiencia y control humano.
  - **CA:** Rubricas IA (campos de coherencia, gramática, pronunciación), override visible, feedback inmediato al usuario.

---

### 10.7 Analítica, Reportes y Dashboards

- **HU-15 Dashboards personalizados y exportación de datos**
  - Como Admin Tenant, quiero dashboards filtrables por fechas/equipos y exportación a CSV/PDF, para análisis operativo y de desempeño.
  - **CA:** Carga < 2s para 10k registros, filtros avanzados, enlaces con expiración, integración con BI externo opcional.

- **HU-16 Métricas de uso y alertas**
  - Como SysAdmin/Admin Tenant, quiero recibir alertas (uso IA, fallas, cuotas), y consultar métricas clave para anticipar riesgos o necesidades de upgrade.
  - **CA:** Alertas automáticas por umbrales (80/90/100%), notificaciones vía Slack/email, logs accesibles por API.

---

### 10.8 Notificaciones, Webhooks e Integraciones

- **HU-17 Sistema de notificaciones y recordatorios**
  - Como usuario, quiero recibir notificaciones push/email sobre eventos importantes (corrección finalizada, expiración de intentos, cambios de curso), para mantenerme informado.
  - **CA:** Notificaciones inmediatas y programadas, historial, "mark as read", opción de mute.

- **HU-18 Webhooks e integraciones externas**
  - Como Admin Tenant, quiero integrar la plataforma con sistemas HRIS/LMS/external (por webhooks seguros, HMAC), para sincronizar flujos de información.
  - **CA:** Retry automático en fallas, logs de eventos, endpoints documentados.

---

### 10.9 Backups, Recuperación y Auditoría

- **HU-19 Backups automáticos y restauración granular**
  - Como SysAdmin, quiero que los backups sean automáticos, seguros y recuperables a nivel tenant, para resiliencia y cumplimiento.
  - **CA:** Backups diarios, restauración selectiva con dry run, logs de restauración y simulacros regulares.

- **HU-20 Auditoría y trazabilidad de cambios**
  - Como Admin Tenant/SysAdmin, quiero que todos los cambios críticos (usuarios, permisos, datos sensibles) queden auditados, para garantizar trazabilidad y compliance.
  - **CA:** Audit logs accesibles vía dashboard/API, exportación CSV, cumplimiento GDPR/FERPA.

---

### 10.10 Facturación y Planes de Suscripción

- **HU-21 Gestión de planes, upgrade/downgrade y facturación**
  - Como Admin Tenant, quiero poder actualizar el plan de suscripción, visualizar costos/uso y descargar facturas, para controlar mis recursos y presupuesto.
  - **CA:** Upgrade/downgrade automático vía Stripe, facturas PDF, alertas de sobreuso, bloqueo/desbloqueo automático por impago.

---

### 10.11 Cumplimiento, Accesibilidad e Internacionalización

- **HU-22 Soporte de idiomas y formatos regionales**
  - Como usuario, quiero poder usar la plataforma en mi idioma preferido (al menos inglés/español), y ver fechas/números en formato local.
  - **CA:** Cambio dinámico de idioma, todas las cadenas externalizadas, formatos regionales persistentes.

- **HU-23 Cumplimiento de accesibilidad (WCAG 2.1 AA)**
  - Como usuario con discapacidad, quiero navegar y operar la plataforma con teclado/lectores de pantalla, para accesibilidad total.
  - **CA:** Pruebas automatizadas/manuales de a11y, score Lighthouse ≥ 90, feedback de usabilidad.

---

### 10.12 Soporte Mobile/PWA y Experiencia Offline

- **HU-24 PWA y experiencia offline**
  - Como usuario móvil, quiero poder acceder y responder exámenes/cursos offline, con sincronización automática al recuperar conexión.
  - **CA:** IndexedDB ≤ 50MB, sync transparente, banners de estado, feedback ante reconexión.

---

### 10.13 Extensibilidad y Plugins

- **HU-25 Plugins/extensiones WebAssembly**
  - Como Tenant avanzado, quiero instalar plugins/extensiones que agreguen nuevas capacidades (chequeos, workflows, analítica), sin downtime.
  - **CA:** Sistema de plugins seguro y versionado, UI para gestión, aislamiento por tenant, logs de actividad.

---

## 12. Flujos Clave Detallados

Cada sub‑sección incluye dos tablas complementarias:
1. Vista back‑end / DevOps — cómo se orquestan microservicios y colas.
2. Vista front‑end / usuario — pasos e interacciones visibles en la UI.

---

### 12.1 Alta de Tenant y Aprovisionamiento

**Back‑end / DevOps**

| # | Actor / Servicio | Acción | SLA |
|---|------------------|--------|-----|
| 1 | SysAdmin | `POST /tenants` → 201 | ≤ 300 ms |
| 2 | Kafka | Publica `tenant.created` | ≤ 50 ms |
| 3 | Provisioner | Terraform apply (AKS ns, Blob) | ≤ 90 s |
| 4 | Worker (Mail) | `202 Accepted` – Email bienvenida | ≤ 2 s |

**Front‑end / Usuario**

| Paso | Interfaz | Acción del usuario | Resultado / Feedback |
|------|----------|-------------------|----------------------|
| A | Portal SysAdmin | Completa formulario “Create tenant” | Toast “Tenant ready” < 5 s |
| B | Email Admin Tenant | Click en enlace de onboarding | Redirección a **Set Password** |
| C | Wizard onboarding | Configura nombre, logo, dominio | Acceso al dashboard inicial |

---

### 12.2 Generación de Preguntas IA

El Creador de contenido:
1. Selecciona el tipo de pregunta a generar:
   • Writing (abierta de escritura)
   • Listening (conversación + MCQ)
   • Speaking (pregunta abierta con grabación)
2. Introduce un prompt o tema (ejemplo: “Hobbies y tiempo libre”).
3. Define el número de preguntas (N).
4. Pulsa Generar.

Caso Listening: primero se genera un script de conversación corta (≈ 20‑30 s de audio, dos interlocutores A & B). Luego el sistema produce N preguntas de opción múltiple sobre el script y sintetiza el audio con Azure TTS.

**Back‑end / DevOps**

| # | Actor/Servicio | Acción | SLA |
|---|----------------|--------|-----|
| 1 | Content Creator | `POST /questions/generate` payload `{type, prompt, num}` | ≤ 200 ms |
| 2 | Core API | Inserta `GenerationJob` (status=PENDING) | — |
| 3 | Celery worker | Branch logic:<br>• Listening → Llama LLM para script conversación (2 speakers, ~150 palabras) | ≤ 8 s |
| 4 | Celery worker | Listening → Llama LLM para N MCQ basadas en script | ≤ 7 s |
| 5 | Celery worker | Writing/Speaking → LLM genera N prompts directos | ≤ 10 s |
| 6 | Celery worker | Si type=listening → Azure TTS convierte script a audio MP3 | ≤ 5 s |
| 7 | Celery worker | Graba TestItems, TestItemOptions, assets (audio URL) | — |
| 8 | Core API | Actualiza GenerationJob a COMPLETED & WebSocket notif | ≤ 500 ms |

**Front‑end / Usuario**

| Paso | Pantalla | Acción del usuario | Feedback UI |
|------|---------|-------------------|-------------|
| A | Generador IA | Selecciona tipo (Writing / Listening / Speaking) | Radio buttons con iconos |
| B | Generador IA | Escribe prompt/tema | Placeholder “Describe topic…” |
| C | Generador IA | Elige N preguntas (slider 1‑20) | Contador dinámico |
| D | Generador IA | Click Generar | Spinner + barra progreso (0‑100 %) |
| E | Si Listening | Estado “1/2: Creando conversación…” → “2/2: Creando preguntas…” | Chips de estado paso‑a‑paso |
| F | Resultado | Lista tarjetas pregunta +<br>• Preview script + botón ► (audio) (Listening)<br>• Prompt abierto (Writing/Speaking) | Badge “AI” + botón Revisar/Editar |

---

### 12.3 Creación y Publicación de Examen

**Back‑end / DevOps**

| # | Servicio | Acción | SLA |
|---|----------|--------|-----|
| 1 | Core API | `POST /exams` → `CoursesExams` (DRAFT) | ≤ 200 ms |
| 2 | Core API | `PUT /exams/{id}/items` – agrega ítems | ≤ 300 ms |
| 3 | Core API | `PUT /exams/{id}` `is_published=true` | ≤ 200 ms |
| 4 | Kafka | Evento `exam.published` | — |

**Front‑end / Usuario**

| Paso | Pantalla | Acción | Feedback |
|------|---------|--------|----------|
| A | Builder examen | Drag‑and‑drop preguntas | Orden se guarda autosave |
| B | Builder examen | Pulsa Publicar | Diálogo confirmación |
| C | Dashboard exámenes | Card pasa a estado Publicado | Etiqueta verde “LIVE” |

---

### 12.4 Intento de Examen y Entrega

**Back‑end / DevOps**

| # | Actor | Acción | SLA |
|---|-------|--------|-----|
| 1 | Candidato | `POST /exam-attempts` (token) | ≤ 150 ms |
| 2 | Core API | Registra ExamAttempts (IN_PROGRESS) | — |
| 3 | UI / API | `GET /attempts/{id}/next` | ≤ 100 ms |
| 4 | Candidato | `POST /answers` (autosave) | ≤ 150 ms |
| 5 | Candidato | `POST /attempts/{id}/finish` | — |
| 6 | Core API | Auto‑score MCQ | ≤ 300 ms |
| 7 | Celery | Job scoring IA | ≤ 4 s por respuesta |
| 8 | Core API | Calcula total_score | — |
| 9 | Worker Mail | Notifica resultado | ≤ 2 min |

**Front‑end / Usuario**

| Paso | Pantalla | Acción | Feedback |
|------|---------|--------|----------|
| A | Landing examen | Clic Iniciar | Temporizador global comienza |
| B | Writing Q | Escribe respuesta | Indicador autosave ✓ |
| C | Listening Q | Reproduce audio | Repetir (1) disponible |
| D | Speaking Q | Graba audio | VU‑meter + pre‑escucha |
| E | Resumen | Pulsa Enviar examen | Modal confirmación |
| F | Pantalla fin | Mensaje “Resultados en breve” | Loading bar progreso IA |

---

### 12.5 Corrección Manual (Override)

**Back‑end / DevOps**

| # | Servicio | Acción | SLA |
|---|----------|--------|-----|
| 1 | Evaluador | `GET /attempts/{id}` | ≤ 200 ms |
| 2 | Evaluador | `PATCH /answers/{id}` (ai_score) | ≤ 150 ms |
| 3 | Core API | Re‑calcula total_score | — |
| 4 | Worker | Envía notificación override | ≤ 1 min |

**Front‑end / Usuario**

| Paso | Pantalla | Acción | Feedback |
|------|---------|--------|----------|
| A | Panel revisión | Edita puntuación slider | Campo cambia a amarillo “Overridden” |
| B | Panel revisión | Pulsa Guardar | Toast “Cambios aplicados” |
| C | Candidato | Recibe email actualización | Link a feedback actualizado |

---

### 12.6 Pipeline de Dashboards y Métricas

**Back‑end / DevOps**

| # | Servicio | Acción | SLA |
|---|----------|--------|-----|
| 1 | Cron Job | Agrega métricas a Metrics | Cada 5 min |
| 2 | Materialized View | Refresca KPI tablas | ≤ 30 s |
| 3 | Grafana | Consulta Prometheus | — |

**Front‑end / Usuario**

| Paso | Pantalla | Acción | Feedback |
|------|---------|--------|----------|
| A | Dashboard Admin | Selecciona rango fechas | Gráfico se refresca < 1 s |
| B | Dashboard Admin | Clic descarga CSV | Archivo inicia descarga |

---

### 12.7 Upgrade/Downgrade de Plan de Suscripción

**Back‑end / DevOps**

| # | Servicio | Acción | SLA |
|---|----------|--------|-----|
| 1 | Admin Tenant | Click Upgrade plan (Stripe) | — |
| 2 | Stripe | `checkout.session.completed` webhook | < 3 s |
| 3 | Core API | Actualiza Tenants.plan | ≤ 200 ms |
| 4 | Worker | Envía factura PDF | ≤ 1 min |

**Front‑end / Usuario**

| Paso | Pantalla | Acción | Feedback |
|------|---------|--------|----------|
| A | Billing page | Selecciona nuevo plan | Redirección Checkout |
| B | Checkout | Completa pago | Spinner + redirect back |
| C | Billing page | Banner “Plan actualizado” | Nuevo límite IA visible |

---

### 12.8 Backup y Restore

**Back‑end / DevOps**

| # | Actor | Acción | SLA |
|---|------|--------|-----|
| 1 | Cron Job | Ejecuta pg_dump + gzip | 02:00 UTC |
| 2 | Script | Verifica checksum | +5 min |
| 3 | Core API | Inserta fila Backups | — |
| 4 | SysAdmin | Dispara restore (manual) | Variable |

**Front‑end / Usuario**

| Paso | Pantalla | Acción | Feedback |
|------|---------|--------|----------|
| A | Console SysAdmin | Ve lista Backups | Badge OK/FAIL |
| B | Backup detail | Click Restore | Modal confirmación |
| C | Console SysAdmin | Progreso restore 0‑100 % | Banner éxito/fracaso |

---

### 12.9 Flujos Mobile/PWA Offline

**Back‑end / DevOps**

| # | Servicio | Acción | SLA |
|---|----------|--------|-----|
| 1 | Service Worker | Cachea `/static/*` | Primer load |
| 2 | IndexedDB | Guarda respuestas offline | Instantáneo |
| 3 | Sync Manager | Re‑envía lote respuestas | < 5 s reconexión |

**Front‑end / Usuario**

| Paso | Pantalla | Acción | Feedback |
|------|---------|--------|----------|
| A | Examen móvil offline | Continúa respondiendo | Banner “Modo offline” |
| B | Reconexión | N/A | Banner cambia a “Sincronizando…” |
| C | Sincronizado | N/A | Toast verde “Respuestas enviadas” |


---

## 13. Índice de API

A continuación, se presenta una estructura jerárquica de endpoints para la plataforma educativa multi-tenant basada en IA, con una breve definición de su propósito o funcionalidad.

---

### 13.1 Autenticación y Seguridad

- **POST `/auth/login`**  
  Inicio de sesión de usuario, retorna tokens JWT.

- **POST `/auth/refresh`**  
  Renueva el token de acceso JWT antes de su expiración.

- **POST `/auth/logout`**  
  Cierra sesión y revoca el token.

- **POST `/auth/mfa/verify`**  
  Verifica el código de segundo factor (MFA).

- **POST `/auth/password/reset`**  
  Inicia el flujo de recuperación de contraseña.

---

### 13.2 Gestión de Tenants

- **POST `/tenants`**  
  Crea un nuevo tenant (organización) en el sistema.

- **GET `/tenants/{id}`**  
  Obtiene información detallada de un tenant.

- **PATCH `/tenants/{id}`**  
  Actualiza configuración y metadatos de un tenant.

- **DELETE `/tenants/{id}`**  
  Desactiva o elimina un tenant.

---

### 13.3 Usuarios y Roles

- **POST `/users`**  
  Crea un nuevo usuario o invita a un usuario a un tenant.

- **GET `/users/{id}`**  
  Recupera perfil y detalles del usuario.

- **PATCH `/users/{id}`**  
  Edita información y preferencias de usuario.

- **GET `/users`**  
  Lista y filtra usuarios de un tenant.

- **POST `/roles`**  
  Crea un nuevo rol dentro de un tenant.

- **GET `/roles`**  
  Lista roles existentes.

- **PATCH `/roles/{id}`**  
  Modifica permisos asociados a un rol.

- **POST `/permissions`**  
  Define un nuevo permiso granular.

- **GET `/permissions`**  
  Lista todos los permisos disponibles.

---

### 13.4 Grupos y Matrículas

- **POST `/groups`**
  Crea un grupo o equipo de usuarios.

- **GET `/groups/{id}`**
  Obtiene información de un grupo.

- **PATCH `/groups/{id}`**
  Modifica grupo (nombre, miembros, cursos).

- **POST `/groups/{id}/users`**
  Añade usuarios a un grupo.

- **POST `/enrollment`**
  Matrícula de usuarios en cursos/grupos.

---

### 13.5 Cursos y Contenido

- **POST `/courses`**  
  Crea un nuevo curso.

- **GET `/courses`**  
  Lista y filtra cursos por tenant, grupo o búsqueda.

- **GET `/courses/{id}`**  
  Detalla la información y el contenido de un curso.

- **PATCH `/courses/{id}`**  
  Edita metadatos y estado del curso.

- **POST `/courses/{id}/items`**  
  Añade lecciones, multimedia o materiales a un curso.

---

### 13.6 Exámenes y Preguntas

- **POST `/exams`**  
  Crea un examen nuevo para un curso.

- **GET `/exams/{id}`**  
  Consulta información y estado del examen.

- **PATCH `/exams/{id}`**  
  Publica, archiva o edita configuración de examen.

- **PUT `/exams/{id}/items`**  
  Asigna preguntas a un examen.

- **POST `/questions`**  
  Crea una pregunta manualmente.

- **GET `/questions`**  
  Lista, filtra y busca preguntas existentes.

- **POST `/questions/generate`**  
  Lanza generación automática de preguntas con IA.

---

### 13.7 IA y Procesamiento Avanzado

- **POST `/ai/generate/writing`**  
  Genera prompts de escritura IA.

- **POST `/ai/generate/listening-script`**  
  Genera scripts de conversación (listening).

- **POST `/ai/generate/listening-mcq`**  
  Genera preguntas MCQ basadas en scripts.

- **POST `/ai/tts`**  
  Sintetiza audio TTS para listening.

- **POST `/ai/stt`**  
  Transcribe audio de respuestas orales.

- **POST `/ai/score/writing`**  
  Califica automáticamente textos escritos.

- **POST `/ai/score/speaking`**  
  Califica automáticamente respuestas orales.

- **POST `/ai/moderate`**  
  Modera o filtra contenido potencialmente ofensivo.

- **POST `/ai/score/plagiarism`**  
  Detecta plagio/similitud en respuestas.

- **GET `/ai/jobs`**  
  Consulta estado y logs de jobs IA.

---

### 13.8 Intentos y Resultados de Examen

- **POST `/exam-attempts`**  
  Inicia un intento de examen para un usuario.

- **GET `/attempts/{id}/next`**  
  Recupera la siguiente pregunta disponible en el intento.

- **POST `/answers`**  
  Registra una respuesta del usuario.

- **POST `/attempts/{id}/finish`**  
  Finaliza el intento y dispara calificación.

- **GET `/reports/exams/{id}`**  
  Recupera resultados detallados del examen.

---

### 13.9 Notificaciones y Comunicación

- **GET `/notifications`**  
  Lista notificaciones para el usuario.

- **PATCH `/notifications/{id}`**  
  Marca notificación como leída.

- **POST `/contact`**  
  Envía mensaje o solicitud de contacto al soporte.

---

### 13.10 Dashboards y Métricas

- **GET `/metrics`**  
  KPIs, uso de IA y actividad del tenant.

- **GET `/dashboard`**  
  Panel de control con analítica y gráficos de uso.

- **GET `/dashboard/download`**  
  Descarga CSV/Excel de métricas.

---

### 13.11 Administración, Backup y Suscripciones

- **GET `/backups`**  
  Lista backups disponibles.

- **POST `/backups/{id}/restore`**  
  Restaura backup seleccionado.

- **GET `/billing/plans`**  
  Consulta los planes de suscripción.

- **POST `/billing/subscribe`**  
  Activa o cambia plan de suscripción.

- **POST `/billing/stripe/webhook`**  
  Procesa eventos de Stripe (webhook).

---

### 13.12 Integraciones y Webhooks

- **POST `/webhooks`**  
  Registra nuevo webhook para integraciones externas.

- **GET `/webhooks`**  
  Lista y consulta webhooks existentes.

- **POST `/webhooks/test`**  
  Prueba envío de webhook.

---

### 13.13 Utilidades y Configuración

- **GET `/config`**  
  Configuración global y del tenant.

- **PATCH `/config`**  
  Edita configuración personalizada.

---

### 13.14 Logs y Auditoría

- **GET `/audit-logs`**  
  Recupera logs de auditoría y acciones del sistema.

- **GET `/system-alerts`**  
  Lista alertas o eventos críticos para admins.

---

---

## 14. Esquema de Base de Datos

-- Arroyo University - Esquema Base de Datos Multi-Tenant IA
-- Optimizado para PostgreSQL 13+, incluye claves UUID, Row-Level Security, y versionado de migraciones.

-- 1. Tenancy
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

CREATE TABLE tenants (
    tenant_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    plan VARCHAR(50) NOT NULL DEFAULT 'free',
    settings JSONB,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. Usuarios, Roles y Permisos
CREATE TABLE users (
    user_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    mfa_enabled BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE roles (
    role_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    name VARCHAR(50) NOT NULL,
    description TEXT
);

CREATE TABLE permissions (
    permission_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT
);

CREATE TABLE user_roles (
    user_id UUID NOT NULL REFERENCES users(user_id),
    role_id UUID NOT NULL REFERENCES roles(role_id),
    PRIMARY KEY (user_id, role_id)
);

CREATE TABLE role_permissions (
    role_id UUID NOT NULL REFERENCES roles(role_id),
    permission_id UUID NOT NULL REFERENCES permissions(permission_id),
    PRIMARY KEY (role_id, permission_id)
);

-- 3. Grupos y Matrícula
CREATE TABLE groups (
    group_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    name VARCHAR(100) NOT NULL,
    description TEXT
);

CREATE TABLE group_users (
    group_id UUID NOT NULL REFERENCES groups(group_id),
    user_id UUID NOT NULL REFERENCES users(user_id),
    PRIMARY KEY (group_id, user_id)
);

-- 4. Cursos y Contenido
CREATE TABLE courses (
    course_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    title VARCHAR(200) NOT NULL,
    description TEXT,
    status VARCHAR(30) DEFAULT 'draft', -- 'draft', 'published', 'archived'
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE course_items (
    item_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    course_id UUID NOT NULL REFERENCES courses(course_id),
    item_type VARCHAR(30) NOT NULL, -- 'video', 'pdf', 'markdown', etc.
    content TEXT,
    media_url TEXT,
    order_index INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE group_courses (
    group_id UUID NOT NULL REFERENCES groups(group_id),
    course_id UUID NOT NULL REFERENCES courses(course_id),
    PRIMARY KEY (group_id, course_id)
);

-- 5. Exámenes y Preguntas
CREATE TABLE exams (
    exam_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    course_id UUID NOT NULL REFERENCES courses(course_id),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    title VARCHAR(200) NOT NULL,
    description TEXT,
    time_limit INT,
    is_published BOOLEAN DEFAULT FALSE,
    version INT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE questions (
    question_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    question_type VARCHAR(50) NOT NULL, -- 'writing', 'listening', 'speaking', 'mcq', etc.
    prompt TEXT NOT NULL,
    metadata JSONB,
    audio_url TEXT,
    correct_answer TEXT,
    order_index INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE exam_items (
    exam_id UUID NOT NULL REFERENCES exams(exam_id),
    question_id UUID NOT NULL REFERENCES questions(question_id),
    PRIMARY KEY (exam_id, question_id)
);

CREATE TABLE question_options (
    option_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    question_id UUID NOT NULL REFERENCES questions(question_id),
    option_text TEXT NOT NULL,
    is_correct BOOLEAN DEFAULT FALSE,
    order_index INT
);

-- 6. Intentos de Examen y Respuestas
CREATE TABLE exam_attempts (
    attempt_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    exam_id UUID NOT NULL REFERENCES exams(exam_id),
    user_id UUID NOT NULL REFERENCES users(user_id),
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    submitted_at TIMESTAMP,
    total_score DECIMAL(6,2),
    status VARCHAR(30) DEFAULT 'in_progress' -- 'in_progress', 'submitted', 'graded'
);

CREATE TABLE answers (
    answer_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    attempt_id UUID NOT NULL REFERENCES exam_attempts(attempt_id),
    question_id UUID NOT NULL REFERENCES questions(question_id),
    user_answer TEXT,
    ai_score DECIMAL(5,2),
    manual_override_score DECIMAL(5,2),
    is_correct BOOLEAN,
    feedback TEXT,
    answered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 7. IA: Generación y Scoring Jobs
CREATE TABLE ai_jobs (
    job_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    job_type VARCHAR(50) NOT NULL, -- 'generate', 'score', 'tts', etc.
    input_payload JSONB,
    output_payload JSONB,
    status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'running', 'completed', 'failed'
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP
);

-- 8. Notificaciones y Mensajes
CREATE TABLE notifications (
    notification_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    user_id UUID NOT NULL REFERENCES users(user_id),
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    notification_type VARCHAR(50),
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE contact_messages (
    message_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID REFERENCES tenants(tenant_id),
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    subject VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_read BOOLEAN DEFAULT FALSE
);

-- 9. Métricas, Dashboards y Logs
CREATE TABLE metrics (
    metric_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    metric_type VARCHAR(100) NOT NULL,
    value NUMERIC,
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE audit_logs (
    log_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    user_id UUID REFERENCES users(user_id),
    action VARCHAR(200) NOT NULL,
    target_type VARCHAR(100),
    target_id UUID,
    payload JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 10. Backups y Facturación
CREATE TABLE backups (
    backup_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    status VARCHAR(30) DEFAULT 'ready',
    backup_url TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    restored_at TIMESTAMP
);

CREATE TABLE invoices (
    invoice_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    user_id UUID REFERENCES users(user_id),
    plan VARCHAR(50),
    amount DECIMAL(10,2),
    pdf_url TEXT,
    issued_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    paid_at TIMESTAMP
);

-- 11. Webhooks e Integraciones
CREATE TABLE webhooks (
    webhook_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    url TEXT NOT NULL,
    event_type VARCHAR(50) NOT NULL,
    secret TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE integrations (
    integration_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    service VARCHAR(100) NOT NULL,
    config JSONB,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 12. Configuración y Preferencias
CREATE TABLE preferences (
    preference_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(user_id),
    locale VARCHAR(10) DEFAULT 'es',
    timezone VARCHAR(50) DEFAULT 'America/Mexico_City',
    settings JSONB,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 13. Soporte a versionado y eliminación lógica
-- (Las tablas clave incluyen created_at/updated_at y un campo is_active donde sea relevante.)

-- 14. Índices críticos para rendimiento multi-tenant
CREATE INDEX idx_users_tenant_id ON users(tenant_id);
CREATE INDEX idx_courses_tenant_id ON courses(tenant_id);
CREATE INDEX idx_exams_tenant_id ON exams(tenant_id);
CREATE INDEX idx_questions_tenant_id ON questions(tenant_id);
CREATE INDEX idx_metrics_tenant_id ON metrics(tenant_id);

-- 15. Seguridad: Activar Row Level Security en tablas core
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE courses ENABLE ROW LEVEL SECURITY;
ALTER TABLE exams ENABLE ROW LEVEL SECURITY;
ALTER TABLE questions ENABLE ROW LEVEL SECURITY;

-- (Agregar políticas de RLS específicas según lógica de acceso.)

-- FIN DEL ESQUEMA BASE

---

## 15. Anexo Técnico Complementario

### 15.1 Reglas y Constraints Avanzados

#### 15.1.1 Claves Foráneas y Eliminación en Cascada

* **Recomendación:** Todas las tablas relacionales deben definir explícitamente `ON DELETE CASCADE` en claves foráneas relevantes para mantener la integridad referencial y simplificar operaciones de borrado lógico por tenant, usuario, curso y examen.
* **Ejemplo:**

```sql
ALTER TABLE courses
    ADD CONSTRAINT fk_courses_tenant FOREIGN KEY (tenant_id) REFERENCES tenants(tenant_id) ON DELETE CASCADE;
ALTER TABLE course_items
    ADD CONSTRAINT fk_course_items_course FOREIGN KEY (course_id) REFERENCES courses(course_id) ON DELETE CASCADE;
ALTER TABLE exam_items
    ADD CONSTRAINT fk_exam_items_exam FOREIGN KEY (exam_id) REFERENCES exams(exam_id) ON DELETE CASCADE;
ALTER TABLE exam_items
    ADD CONSTRAINT fk_exam_items_question FOREIGN KEY (question_id) REFERENCES questions(question_id) ON DELETE CASCADE;
```

#### 15.1.2 Unicidad, Índices y Composición de Claves

* Definir índices compuestos por `tenant_id` + campo buscado en todas las tablas multi-tenant con alto volumen de búsqueda o relación N\:M (ej. `users`, `courses`, `exams`, `metrics`).
* Usar constraints de unicidad para evitar registros duplicados por tenant:

```sql
CREATE UNIQUE INDEX uniq_username_tenant ON users(username, tenant_id);
CREATE UNIQUE INDEX uniq_group_name_tenant ON groups(name, tenant_id);
```

#### 15.1.3 Versionado y Auditoría

* Incluir columnas `version INT`, `is_archived BOOLEAN` y campos de tracking como `created_by`, `updated_by` y timestamps de cambios donde sean relevantes (ej. cursos, exámenes, preguntas, roles).

#### 15.1.4 Lógica Temporal y Vencimiento

* Tablas que requieran expiración (roles temporales, invitaciones, intentos) deben tener campos `active_from`, `expires_at`, y triggers automáticos o cronjobs para revocación de permisos o depuración de datos expirados.

### 15.2 Estructuras y Relaciones Faltantes

#### 15.2.1 Puentes y Relaciones N\:M

* Definir explícitamente las tablas puente para N\:M:

  * `user_roles (user_id, role_id)`
  * `role_permissions (role_id, permission_id)`
  * `group_users (group_id, user_id)`
  * `group_courses (group_id, course_id)`
  * `exam_items (exam_id, question_id)`
* Mantener consistencia de claves foráneas y claves primarias compuestas.

#### 15.2.2 Seguimiento de Cambios y Log de Auditoría

* Ampliar la tabla de `audit_logs` para incluir los siguientes campos:

  * `entity` (tipo de objeto afectado, ej. "user", "exam", "course")
  * `entity_id` (UUID del objeto)
  * `action` ("CREATE", "UPDATE", "DELETE", "LOGIN", "ASSIGN\_ROLE", etc.)
  * `diff` (campo JSONB que registre el cambio específico)

```sql
ALTER TABLE audit_logs ADD COLUMN entity VARCHAR(50);
ALTER TABLE audit_logs ADD COLUMN entity_id UUID;
ALTER TABLE audit_logs ADD COLUMN diff JSONB;
```

#### 15.2.3 Métricas y Monitoring

* Incluir tablas auxiliares para tags, métricas personalizadas y monitoring items:

```sql
CREATE TABLE metric_tags (
    tag_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    metric_id UUID NOT NULL REFERENCES metrics(metric_id) ON DELETE CASCADE,
    tag VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE dashboards (
    dashboard_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    name VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE monitoring_items (
    item_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    dashboard_id UUID NOT NULL REFERENCES dashboards(dashboard_id) ON DELETE CASCADE,
    item_name VARCHAR(100),
    item_value TEXT
);
```

#### 15.2.4 Webhook Logs y Jobs de IA

* Definir tabla de logs de webhook y detalle granular de jobs de IA:

```sql
CREATE TABLE webhook_logs (
    log_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    endpoint VARCHAR(150) NOT NULL,
    payload JSONB NOT NULL,
    response_code INT,
    attempt INT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

* Incluir detalles de jobs de IA: tipo, input, output, status, errores, timestamps.

### 15.3 Criterios Técnicos de Aceptación Avanzados

#### 15.3.1 Validación y Auditoría de Acciones Críticas

* Todas las operaciones de CRUD sensibles (usuarios, roles, cursos, exámenes) deben:

  * Registrar evento en audit\_logs, con usuario, acción, diff JSONB y timestamps.
  * Rechazar operaciones inconsistentes vía constraints y triggers.

### 3.2 SLAs de API y Eventos Asíncronos

* CRUD estándar: p95 < 200 ms.
* Generación/corrección IA: latencia < 15 s para lotes de hasta 5 ítems.
* Eventos críticos (creación tenant, generación preguntas, publicación exámenes): feedback visual y logs auditable en tiempo real.

### 3.3 Seguridad y Cumplimiento

* Row-Level Security habilitado y políticas configuradas en todas las tablas core.
* Hashing de contraseñas: `bcrypt` obligatorio.
* MFA forzable por política de tenant.
* Logs automáticos para DSR (Data Subject Rights) y acceso a datos sensibles.

### 3.4 Disaster Recovery y Backups

* Backups automáticos diarios, retención 30 días, restauración selectiva, simulacros semestrales, RPO 24h, RTO 4h.
* Logs y métricas de éxito/falla de backup accesibles por API y dashboard.

### 3.5 Pruebas y Observabilidad

* Cobertura de pruebas automatizadas ≥ 80% en código crítico.
* Tracing, logging y monitoreo 100% centralizado.
* Validaciones automáticas en pipelines CI/CD para migraciones y despliegues.

## 4. Plantillas SQL Específicas para Migraciones

### 4.1 Ejemplo de Migración Agregando Constraints Faltantes

```sql
ALTER TABLE answers
    ADD CONSTRAINT fk_answers_attempt FOREIGN KEY (attempt_id) REFERENCES exam_attempts(attempt_id) ON DELETE CASCADE,
    ADD CONSTRAINT fk_answers_question FOREIGN KEY (question_id) REFERENCES questions(question_id) ON DELETE CASCADE;

ALTER TABLE group_courses
    ADD CONSTRAINT fk_group_courses_group FOREIGN KEY (group_id) REFERENCES groups(group_id) ON DELETE CASCADE,
    ADD CONSTRAINT fk_group_courses_course FOREIGN KEY (course_id) REFERENCES courses(course_id) ON DELETE CASCADE;
```

### 4.2 Políticas Básicas de Row-Level Security (RLS)

```sql
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
CREATE POLICY tenant_isolation_users ON users USING (tenant_id = current_setting('app.current_tenant')::uuid);
ALTER TABLE courses ENABLE ROW LEVEL SECURITY;
CREATE POLICY tenant_isolation_courses ON courses USING (tenant_id = current_setting('app.current_tenant')::uuid);
```

## 5. Notas y Recomendaciones Finales

* Mantener **consistencia de nombres** y convenciones REST/Snake\_case en la base de datos y API.
* Incorporar documentación técnica de migraciones y constraints en el repositorio junto con los modelos Pydantic/ORM.
* Revisar periódicamente la cobertura y calidad de los CA técnicos conforme avancen las releases.
* Todo cambio en el modelo debe ser migrable reversiblemente y probado en CI.

---
