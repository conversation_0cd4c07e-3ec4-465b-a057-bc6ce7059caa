# Guía de Implementación - Arroyo University

## Introducción

Esta guía proporciona un roadmap paso a paso para implementar Arroyo University, desde la configuración inicial hasta el despliegue en producción. Incluye prerequisitos, configuración de entornos, deployment y validación.

---

## 1. Prerequisitos y Preparación

### 1.1 Requisitos del Sistema

#### Infraestructura Mínima
| Componente | Desarrollo | Staging | Producción |
|------------|------------|---------|------------|
| **Kubernetes** | minikube/k3s | AKS 3 nodes | AKS 5+ nodes |
| **Database** | PostgreSQL 15 | Azure DB Flexible | Azure DB HA |
| **Storage** | Local/MinIO | Azure Blob | Azure Blob + CDN |
| **Memory** | 8GB | 32GB | 128GB+ |
| **CPU** | 4 cores | 8 cores | 32+ cores |

#### Cuentas y Servicios Externos
```bash
# Required external services
OPENAI_API_KEY="sk-..."
AZURE_SPEECH_KEY="..."
AZURE_SPEECH_REGION="eastus"
STRIPE_SECRET_KEY="sk_test_..."
SENDGRID_API_KEY="SG...."

# Azure Resources
AZURE_SUBSCRIPTION_ID="..."
AZURE_TENANT_ID="..."
AZURE_CLIENT_ID="..."
AZURE_CLIENT_SECRET="..."
```

### 1.2 Herramientas de Desarrollo

#### Instalación de Herramientas
```bash
# Install required tools
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# Kubernetes CLI
curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
sudo install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl

# Helm
curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash

# Terraform
wget https://releases.hashicorp.com/terraform/1.6.0/terraform_1.6.0_linux_amd64.zip
unzip terraform_1.6.0_linux_amd64.zip
sudo mv terraform /usr/local/bin/

# Azure CLI
curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash
```

---

## 2. Configuración de Entorno de Desarrollo

### 2.1 Clonar y Configurar Repositorio

```bash
# Clone repository
git clone https://github.com/arroyo-university/platform.git
cd platform

# Setup environment
cp .env.example .env.local
# Edit .env.local with your configuration

# Install dependencies
# Backend
cd backend
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt
pip install -r requirements-dev.txt

# Frontend
cd ../frontend
npm install

# Database setup
cd ../database
docker-compose up -d postgres
alembic upgrade head
```

### 2.2 Configuración Local

#### Docker Compose para Desarrollo
```yaml
# docker-compose.dev.yml
version: '3.8'
services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: arroyo_dev
      POSTGRES_USER: arroyo
      POSTGRES_PASSWORD: dev_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

  minio:
    image: minio/minio
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data

volumes:
  postgres_data:
  minio_data:
```

#### Configuración de Variables de Entorno
```bash
# .env.local
DATABASE_URL="postgresql://arroyo:dev_password@localhost:5432/arroyo_dev"
REDIS_URL="redis://localhost:6379"
SECRET_KEY="dev-secret-key-change-in-production"

# AI Services
OPENAI_API_KEY="your-openai-key"
AZURE_SPEECH_KEY="your-azure-speech-key"
AZURE_SPEECH_REGION="eastus"

# Storage
STORAGE_TYPE="minio"
MINIO_ENDPOINT="localhost:9000"
MINIO_ACCESS_KEY="minioadmin"
MINIO_SECRET_KEY="minioadmin"

# Email
SENDGRID_API_KEY="your-sendgrid-key"
FROM_EMAIL="<EMAIL>"

# Development flags
DEBUG=true
LOG_LEVEL="DEBUG"
ENABLE_CORS=true
```

### 2.3 Ejecutar en Desarrollo

```bash
# Terminal 1: Backend API
cd backend
source venv/bin/activate
uvicorn main:app --reload --host 0.0.0.0 --port 8000

# Terminal 2: Frontend
cd frontend
npm run dev

# Terminal 3: Celery Worker
cd backend
celery -A app.celery worker --loglevel=info

# Terminal 4: Celery Beat (scheduler)
cd backend
celery -A app.celery beat --loglevel=info
```

---

## 3. Configuración de Staging

### 3.1 Infraestructura con Terraform

#### Configuración de Terraform
```hcl
# environments/staging/main.tf
terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~>3.0"
    }
  }
  
  backend "azurerm" {
    resource_group_name  = "terraform-state-rg"
    storage_account_name = "arroyoterraformstate"
    container_name       = "tfstate"
    key                  = "staging.terraform.tfstate"
  }
}

module "arroyo_staging" {
  source = "../../modules/arroyo-platform"
  
  environment = "staging"
  location    = "East US"
  
  # AKS Configuration
  aks_node_count = 3
  aks_vm_size    = "Standard_D2s_v3"
  
  # Database Configuration
  db_sku_name = "GP_Standard_D2s_v3"
  db_storage_mb = 32768
  
  # Application Configuration
  app_replicas = 2
  enable_autoscaling = true
  min_replicas = 2
  max_replicas = 10
}
```

#### Desplegar Infraestructura
```bash
cd terraform/environments/staging

# Initialize Terraform
terraform init

# Plan deployment
terraform plan -var-file="staging.tfvars"

# Apply configuration
terraform apply -var-file="staging.tfvars"

# Get AKS credentials
az aks get-credentials --resource-group arroyo-staging-rg --name arroyo-staging-aks
```

### 3.2 Configuración de Kubernetes

#### Namespace y Secrets
```bash
# Create namespace
kubectl create namespace arroyo-staging

# Create secrets
kubectl create secret generic app-secrets \
  --from-literal=database-url="postgresql://..." \
  --from-literal=openai-api-key="sk-..." \
  --from-literal=azure-speech-key="..." \
  --namespace=arroyo-staging

# Create TLS secret
kubectl create secret tls arroyo-tls \
  --cert=path/to/tls.crt \
  --key=path/to/tls.key \
  --namespace=arroyo-staging
```

#### Helm Chart Deployment
```bash
# Add Helm repository
helm repo add arroyo https://charts.arroyo.app
helm repo update

# Install application
helm install arroyo-staging arroyo/arroyo-platform \
  --namespace arroyo-staging \
  --values values-staging.yaml \
  --set image.tag=staging-latest
```

---

## 4. Despliegue en Producción

### 4.1 Preparación Pre-Producción

#### Checklist de Seguridad
```bash
# Security checklist script
#!/bin/bash

echo "🔒 Running security checklist..."

# Check for secrets in code
echo "Checking for hardcoded secrets..."
git secrets --scan

# Vulnerability scan
echo "Scanning for vulnerabilities..."
trivy image arroyoregistry.azurecr.io/core-api:latest

# Infrastructure security scan
echo "Scanning infrastructure..."
checkov -d terraform/ --framework terraform

# SSL/TLS configuration
echo "Checking SSL configuration..."
testssl.sh --quiet api.arroyo.app

echo "✅ Security checklist completed"
```

#### Performance Testing
```bash
# Load testing script
#!/bin/bash

echo "🚀 Running performance tests..."

# API load test
k6 run --vus 100 --duration 5m tests/load/api-test.js

# Database performance test
pgbench -h $DB_HOST -U $DB_USER -d $DB_NAME -c 10 -j 2 -T 300

# Frontend performance test
lighthouse --chrome-flags="--headless" https://app.arroyo.app

echo "✅ Performance tests completed"
```

### 4.2 Blue-Green Deployment

#### Deployment Script
```bash
#!/bin/bash
# blue-green-deploy.sh

set -e

NEW_VERSION=$1
CURRENT_COLOR=$(kubectl get service arroyo-app-service -o jsonpath='{.spec.selector.version}')

if [ "$CURRENT_COLOR" = "blue" ]; then
    NEW_COLOR="green"
else
    NEW_COLOR="blue"
fi

echo "🚀 Deploying version $NEW_VERSION to $NEW_COLOR environment"

# Update deployment with new version
kubectl set image deployment/arroyo-app-$NEW_COLOR \
  core-api=arroyoregistry.azurecr.io/core-api:$NEW_VERSION \
  ai-service=arroyoregistry.azurecr.io/ai-service:$NEW_VERSION

# Wait for rollout to complete
kubectl rollout status deployment/arroyo-app-$NEW_COLOR --timeout=600s

# Health check
echo "🏥 Running health checks..."
for i in {1..30}; do
  if curl -f http://arroyo-app-$NEW_COLOR-service/health; then
    echo "✅ Health check passed"
    break
  fi
  echo "⏳ Waiting for health check... ($i/30)"
  sleep 10
done

# Smoke tests
echo "🧪 Running smoke tests..."
npm run test:smoke -- --env=$NEW_COLOR

# Switch traffic
echo "🔄 Switching traffic to $NEW_COLOR"
kubectl patch service arroyo-app-service -p '{"spec":{"selector":{"version":"'$NEW_COLOR'"}}}'

# Verify traffic switch
sleep 30
curl -f https://api.arroyo.app/health

echo "✅ Deployment completed successfully"
echo "📊 Monitor metrics at: https://grafana.arroyo.app"
```

### 4.3 Rollback Procedure

```bash
#!/bin/bash
# rollback.sh

PREVIOUS_COLOR=$1

echo "🔄 Rolling back to $PREVIOUS_COLOR"

# Switch service back
kubectl patch service arroyo-app-service -p '{"spec":{"selector":{"version":"'$PREVIOUS_COLOR'"}}}'

# Verify rollback
curl -f https://api.arroyo.app/health

echo "✅ Rollback completed"
```

---

## 5. Configuración de Monitoreo

### 5.1 Prometheus y Grafana

#### Prometheus Configuration
```yaml
# prometheus-config.yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "/etc/prometheus/rules/*.yml"

scrape_configs:
  - job_name: 'arroyo-api'
    static_configs:
      - targets: ['arroyo-app-service:8000']
    metrics_path: '/metrics'
    scrape_interval: 10s

  - job_name: 'kubernetes-pods'
    kubernetes_sd_configs:
    - role: pod
    relabel_configs:
    - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
      action: keep
      regex: true
```

#### Grafana Dashboard Import
```bash
# Import dashboards
curl -X POST \
  http://admin:<EMAIL>/api/dashboards/db \
  -H 'Content-Type: application/json' \
  -d @dashboards/arroyo-overview.json
```

### 5.2 Alerting Configuration

```yaml
# alerting-rules.yml
groups:
- name: arroyo.rules
  rules:
  - alert: HighErrorRate
    expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "High error rate detected"
      description: "Error rate is {{ $value }} errors per second"

  - alert: DatabaseConnectionHigh
    expr: pg_stat_activity_count > 80
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "High database connection count"
```

---

## 6. Validación Post-Deployment

### 6.1 Health Checks

```bash
#!/bin/bash
# health-check.sh

echo "🏥 Running comprehensive health checks..."

# API health
curl -f https://api.arroyo.app/health || exit 1

# Database connectivity
curl -f https://api.arroyo.app/health/db || exit 1

# AI services
curl -f https://api.arroyo.app/health/ai || exit 1

# Storage
curl -f https://api.arroyo.app/health/storage || exit 1

# Frontend
curl -f https://app.arroyo.app || exit 1

echo "✅ All health checks passed"
```

### 6.2 Integration Tests

```python
# tests/integration/test_end_to_end.py
import pytest
import asyncio
from httpx import AsyncClient

@pytest.mark.asyncio
async def test_complete_exam_flow():
    async with AsyncClient(base_url="https://api.arroyo.app") as client:
        # Create tenant
        tenant_response = await client.post("/tenants", json={
            "name": "Test Tenant",
            "plan": "basic"
        })
        assert tenant_response.status_code == 201
        
        # Create user
        user_response = await client.post("/users", json={
            "email": "<EMAIL>",
            "username": "testuser"
        })
        assert user_response.status_code == 201
        
        # Create exam
        exam_response = await client.post("/exams", json={
            "title": "Test Exam",
            "time_limit_sec": 3600
        })
        assert exam_response.status_code == 201
        
        # Complete exam flow
        # ... additional test steps
```

### 6.3 Performance Validation

```bash
# Performance validation
echo "📊 Running performance validation..."

# Load test
k6 run --vus 50 --duration 2m tests/load/production-test.js

# Database performance
pgbench -h $PROD_DB_HOST -U $PROD_DB_USER -d $PROD_DB_NAME -c 5 -j 2 -T 60

# Frontend performance
lighthouse --chrome-flags="--headless" https://app.arroyo.app --output=json --output-path=lighthouse-report.json

echo "✅ Performance validation completed"
```

---

## 7. Troubleshooting Común

### 7.1 Problemas de Deployment

| Problema | Síntoma | Solución |
|----------|---------|----------|
| **Pod CrashLoopBackOff** | Pods reiniciando constantemente | Verificar logs: `kubectl logs -f pod-name` |
| **ImagePullBackOff** | No puede descargar imagen | Verificar registry credentials |
| **Service Unavailable** | 503 errors | Verificar health checks y readiness probes |
| **Database Connection** | Connection timeout | Verificar network policies y secrets |

### 7.2 Comandos de Diagnóstico

```bash
# Kubernetes diagnostics
kubectl get pods -n arroyo-prod
kubectl describe pod <pod-name> -n arroyo-prod
kubectl logs -f <pod-name> -n arroyo-prod

# Database diagnostics
kubectl exec -it postgres-pod -- psql -U arroyo -d arroyo_prod -c "SELECT version();"

# Network diagnostics
kubectl exec -it debug-pod -- nslookup arroyo-app-service
kubectl exec -it debug-pod -- curl -v http://arroyo-app-service:8000/health
```

---

## 8. Mantenimiento y Actualizaciones

### 8.1 Actualizaciones Regulares

```bash
#!/bin/bash
# maintenance.sh

echo "🔧 Running maintenance tasks..."

# Update dependencies
helm repo update
helm upgrade arroyo-prod arroyo/arroyo-platform --namespace arroyo-prod

# Database maintenance
kubectl exec -it postgres-pod -- psql -U arroyo -d arroyo_prod -c "VACUUM ANALYZE;"

# Clean up old images
docker system prune -f

# Backup verification
./scripts/verify-backups.sh

echo "✅ Maintenance completed"
```

### 8.2 Monitoring de Salud

```python
# health_monitor.py
import asyncio
import aiohttp
from datetime import datetime

async def monitor_health():
    endpoints = [
        "https://api.arroyo.app/health",
        "https://app.arroyo.app",
        "https://ai.arroyo.app/health"
    ]
    
    for endpoint in endpoints:
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(endpoint, timeout=10) as response:
                    if response.status == 200:
                        print(f"✅ {endpoint} - OK")
                    else:
                        print(f"❌ {endpoint} - Status: {response.status}")
        except Exception as e:
            print(f"❌ {endpoint} - Error: {e}")

if __name__ == "__main__":
    asyncio.run(monitor_health())
```

---

## Conclusión

Esta guía de implementación proporciona un roadmap completo para desplegar Arroyo University desde desarrollo hasta producción. Siguiendo estos pasos y mejores prácticas, se asegura un deployment exitoso, seguro y mantenible de la plataforma educativa.
