<!DOCTYPE html>
<html lang="es" class="h-full bg-gray-50">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Corrección Manual - Arroyo University</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
    </style>
</head>
<body class="h-full">
    <div class="min-h-full">
        <!-- Navigation -->
        <nav class="bg-white shadow">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.84L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3z"/>
                            </svg>
                        </div>
                        <h1 class="ml-3 text-xl font-semibold text-gray-900">Universidad Ejemplo</h1>
                        <nav class="ml-8 flex space-x-4">
                            <a href="#" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">Dashboard</a>
                            <a href="#" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">Exámenes</a>
                            <a href="#" class="bg-blue-100 text-blue-700 px-3 py-2 rounded-md text-sm font-medium">Corrección</a>
                            <a href="#" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">Reportes</a>
                        </nav>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                            <span class="text-sm font-medium text-gray-700">MG</span>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="max-w-6xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="mb-8">
                <div class="flex items-center justify-between">
                    <div>
                        <h2 class="text-2xl font-bold text-gray-900">Corrección Manual</h2>
                        <p class="mt-1 text-gray-600">Revisa y ajusta las calificaciones automáticas</p>
                    </div>
                    <div class="flex items-center space-x-3">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                            Pendientes: 12
                        </span>
                        <button class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                            Filtros
                        </button>
                    </div>
                </div>
            </div>

            <!-- Exam Info -->
            <div class="bg-white shadow rounded-lg mb-6">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-lg font-medium text-gray-900">English B2 Assessment</h3>
                            <p class="text-sm text-gray-600">Candidato: Carlos López • Completado: 15 Nov 2023, 14:30</p>
                        </div>
                        <div class="flex items-center space-x-4">
                            <div class="text-right">
                                <p class="text-sm text-gray-600">Puntuación IA</p>
                                <p class="text-lg font-semibold text-blue-600">78/100</p>
                            </div>
                            <div class="text-right">
                                <p class="text-sm text-gray-600">Nivel CEFR</p>
                                <p class="text-lg font-semibold text-green-600">B2.1</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Question Review -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Question List -->
                <div class="lg:col-span-1">
                    <div class="bg-white shadow rounded-lg">
                        <div class="px-4 py-3 border-b border-gray-200">
                            <h4 class="text-sm font-medium text-gray-900">Preguntas para Revisar</h4>
                        </div>
                        <div class="divide-y divide-gray-200">
                            <!-- Writing Questions -->
                            <div class="p-4">
                                <h5 class="text-xs font-medium text-gray-500 uppercase tracking-wide mb-3">Writing</h5>
                                <div class="space-y-2">
                                    <button onclick="selectQuestion(1)" 
                                            class="w-full text-left p-3 rounded-lg border-2 border-blue-200 bg-blue-50 hover:bg-blue-100 transition-colors">
                                        <div class="flex items-center justify-between">
                                            <span class="text-sm font-medium text-blue-900">Pregunta 1</span>
                                            <div class="flex items-center space-x-2">
                                                <span class="text-xs text-blue-700">IA: 4.2/5</span>
                                                <svg class="w-4 h-4 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92z" clip-rule="evenodd"/>
                                                </svg>
                                            </div>
                                        </div>
                                        <p class="text-xs text-blue-700 mt-1">Confianza IA: 68%</p>
                                    </button>
                                    
                                    <button onclick="selectQuestion(2)" 
                                            class="w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors">
                                        <div class="flex items-center justify-between">
                                            <span class="text-sm font-medium text-gray-900">Pregunta 2</span>
                                            <div class="flex items-center space-x-2">
                                                <span class="text-xs text-gray-600">IA: 3.8/5</span>
                                                <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                                                </svg>
                                            </div>
                                        </div>
                                        <p class="text-xs text-gray-500 mt-1">Confianza IA: 89%</p>
                                    </button>
                                </div>
                            </div>
                            
                            <!-- Speaking Questions -->
                            <div class="p-4">
                                <h5 class="text-xs font-medium text-gray-500 uppercase tracking-wide mb-3">Speaking</h5>
                                <div class="space-y-2">
                                    <button onclick="selectQuestion(3)" 
                                            class="w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors">
                                        <div class="flex items-center justify-between">
                                            <span class="text-sm font-medium text-gray-900">Pregunta 23</span>
                                            <div class="flex items-center space-x-2">
                                                <span class="text-xs text-gray-600">IA: 4.5/5</span>
                                                <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                                                </svg>
                                            </div>
                                        </div>
                                        <p class="text-xs text-gray-500 mt-1">Confianza IA: 92%</p>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Question Detail -->
                <div class="lg:col-span-2">
                    <div class="bg-white shadow rounded-lg">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <div class="flex items-center justify-between">
                                <h4 class="text-lg font-medium text-gray-900">Pregunta 1 - Writing</h4>
                                <div class="flex items-center space-x-2">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        Requiere revisión
                                    </span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="p-6">
                            <!-- Question Text -->
                            <div class="mb-6">
                                <h5 class="text-sm font-medium text-gray-900 mb-2">Pregunta:</h5>
                                <div class="bg-gray-50 p-4 rounded-lg">
                                    <p class="text-gray-700">
                                        Describe tu hobby favorito y explica por qué te gusta tanto. En tu respuesta, incluye: qué actividad realizas, cuándo la practicas, con quién la compartes y qué beneficios te aporta. Escribe entre 100-150 palabras.
                                    </p>
                                </div>
                            </div>

                            <!-- Student Response -->
                            <div class="mb-6">
                                <h5 class="text-sm font-medium text-gray-900 mb-2">Respuesta del estudiante:</h5>
                                <div class="bg-blue-50 border border-blue-200 p-4 rounded-lg">
                                    <p class="text-gray-800 leading-relaxed">
                                        Mi hobby favorito es la fotografía. Me gusta mucho porque me permite capturar momentos especiales y expresar mi creatividad. Practico este hobby principalmente los fines de semana cuando tengo más tiempo libre. A veces voy solo a explorar nuevos lugares, pero también me gusta ir con mis amigos que comparten esta pasión.
                                        <br><br>
                                        La fotografía me aporta muchos beneficios. Primero, me ayuda a relajarme y olvidar el estrés del trabajo. Segundo, me permite conocer lugares nuevos y interesantes en mi ciudad. Además, he mejorado mucho mi técnica y ahora puedo tomar fotos más profesionales. También he conocido a muchas personas interesantes en grupos de fotografía.
                                        <br><br>
                                        En conclusión, la fotografía es más que un hobby para mí, es una forma de ver el mundo de manera diferente.
                                    </p>
                                    <div class="mt-3 text-xs text-blue-600">
                                        Palabras: 142 • Tiempo empleado: 8 minutos
                                    </div>
                                </div>
                            </div>

                            <!-- AI Scoring -->
                            <div class="mb-6">
                                <h5 class="text-sm font-medium text-gray-900 mb-3">Evaluación de IA</h5>
                                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                                    <div class="bg-gray-50 p-3 rounded-lg">
                                        <p class="text-xs text-gray-600">Coherencia</p>
                                        <p class="text-lg font-semibold text-gray-900">4.5/5</p>
                                    </div>
                                    <div class="bg-gray-50 p-3 rounded-lg">
                                        <p class="text-xs text-gray-600">Gramática</p>
                                        <p class="text-lg font-semibold text-gray-900">4.0/5</p>
                                    </div>
                                    <div class="bg-gray-50 p-3 rounded-lg">
                                        <p class="text-xs text-gray-600">Vocabulario</p>
                                        <p class="text-lg font-semibold text-gray-900">4.2/5</p>
                                    </div>
                                    <div class="bg-gray-50 p-3 rounded-lg">
                                        <p class="text-xs text-gray-600">Estructura</p>
                                        <p class="text-lg font-semibold text-gray-900">4.0/5</p>
                                    </div>
                                </div>
                                
                                <div class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                                    <p class="text-sm text-yellow-800">
                                        <strong>Comentario IA:</strong> Respuesta bien estructurada que cumple con todos los requisitos. El vocabulario es apropiado para el nivel B2. Se detectaron algunos errores menores de gramática que no afectan la comprensión general.
                                    </p>
                                    <p class="text-xs text-yellow-600 mt-2">Confianza: 68% (Requiere revisión manual)</p>
                                </div>
                            </div>

                            <!-- Manual Override -->
                            <div class="border-t pt-6">
                                <h5 class="text-sm font-medium text-gray-900 mb-4">Corrección Manual</h5>
                                
                                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                                    <div>
                                        <label class="block text-xs text-gray-600 mb-1">Coherencia</label>
                                        <input type="range" min="0" max="5" step="0.1" value="4.5" 
                                               class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer">
                                        <div class="flex justify-between text-xs text-gray-500 mt-1">
                                            <span>0</span>
                                            <span class="font-medium">4.5</span>
                                            <span>5</span>
                                        </div>
                                    </div>
                                    
                                    <div>
                                        <label class="block text-xs text-gray-600 mb-1">Gramática</label>
                                        <input type="range" min="0" max="5" step="0.1" value="4.2" 
                                               class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer">
                                        <div class="flex justify-between text-xs text-gray-500 mt-1">
                                            <span>0</span>
                                            <span class="font-medium text-yellow-600">4.2</span>
                                            <span>5</span>
                                        </div>
                                    </div>
                                    
                                    <div>
                                        <label class="block text-xs text-gray-600 mb-1">Vocabulario</label>
                                        <input type="range" min="0" max="5" step="0.1" value="4.2" 
                                               class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer">
                                        <div class="flex justify-between text-xs text-gray-500 mt-1">
                                            <span>0</span>
                                            <span class="font-medium">4.2</span>
                                            <span>5</span>
                                        </div>
                                    </div>
                                    
                                    <div>
                                        <label class="block text-xs text-gray-600 mb-1">Estructura</label>
                                        <input type="range" min="0" max="5" step="0.1" value="4.0" 
                                               class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer">
                                        <div class="flex justify-between text-xs text-gray-500 mt-1">
                                            <span>0</span>
                                            <span class="font-medium">4.0</span>
                                            <span>5</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Overall Score -->
                                <div class="mb-4">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        Puntuación Final: <span class="text-lg font-bold text-blue-600">4.2/5</span>
                                    </label>
                                    <div class="w-full bg-gray-200 rounded-full h-3">
                                        <div class="bg-blue-600 h-3 rounded-full" style="width: 84%"></div>
                                    </div>
                                </div>

                                <!-- Comments -->
                                <div class="mb-4">
                                    <label for="comments" class="block text-sm font-medium text-gray-700 mb-2">
                                        Comentarios para el estudiante
                                    </label>
                                    <textarea id="comments" rows="4" 
                                              placeholder="Proporciona feedback específico sobre la respuesta..."
                                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">Excelente respuesta que cumple con todos los requisitos. El texto está bien estructurado y el vocabulario es apropiado. Pequeña mejora en gramática: "he mejorado mucho mi técnica" podría ser "he mejorado mucho en mi técnica". En general, muy buen trabajo.</textarea>
                                </div>

                                <!-- Action Buttons -->
                                <div class="flex justify-between">
                                    <button class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                                        Marcar para revisión posterior
                                    </button>
                                    
                                    <div class="flex space-x-3">
                                        <button class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                                            Cancelar
                                        </button>
                                        <button onclick="saveScore()" 
                                                class="inline-flex items-center px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                                            Guardar Corrección
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        function selectQuestion(questionId) {
            // Remove active state from all questions
            document.querySelectorAll('button[onclick^="selectQuestion"]').forEach(btn => {
                btn.className = btn.className.replace('border-blue-200 bg-blue-50', 'border-gray-200');
                btn.className = btn.className.replace('text-blue-900', 'text-gray-900');
                btn.className = btn.className.replace('text-blue-700', 'text-gray-600');
            });
            
            // Add active state to selected question
            event.target.closest('button').className = event.target.closest('button').className.replace('border-gray-200', 'border-blue-200 bg-blue-50');
            
            // In real implementation, this would load the question data
            console.log('Loading question', questionId);
        }

        function saveScore() {
            // Show success message
            alert('Corrección guardada exitosamente. El estudiante será notificado de la actualización.');
            
            // In real implementation, this would save to backend and update UI
        }
    </script>
</body>
</html>
