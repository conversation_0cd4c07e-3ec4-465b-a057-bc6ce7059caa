<!DOCTYPE html>
<html lang="es" class="h-full bg-gray-50">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pregunta Speaking - Arroyo University</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .recording-pulse {
            animation: pulse 1.5s ease-in-out infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        .audio-wave {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 2px;
        }
        .wave-bar {
            width: 3px;
            background: #3b82f6;
            border-radius: 2px;
            animation: wave 1s ease-in-out infinite;
        }
        .wave-bar:nth-child(1) { height: 10px; animation-delay: 0s; }
        .wave-bar:nth-child(2) { height: 20px; animation-delay: 0.1s; }
        .wave-bar:nth-child(3) { height: 15px; animation-delay: 0.2s; }
        .wave-bar:nth-child(4) { height: 25px; animation-delay: 0.3s; }
        .wave-bar:nth-child(5) { height: 18px; animation-delay: 0.4s; }
        .wave-bar:nth-child(6) { height: 12px; animation-delay: 0.5s; }
        @keyframes wave {
            0%, 100% { transform: scaleY(1); }
            50% { transform: scaleY(0.3); }
        }
    </style>
</head>
<body class="h-full">
    <div class="min-h-full">
        <!-- Header with Timer -->
        <header class="bg-white shadow">
            <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-4">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.84L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3z"/>
                            </svg>
                        </div>
                        <h1 class="ml-3 text-lg font-semibold text-gray-900">English B2 Assessment</h1>
                    </div>
                    
                    <div class="flex items-center space-x-6">
                        <div class="text-sm text-gray-600">
                            Pregunta <span class="font-medium">23 de 25</span>
                        </div>
                        <div class="flex items-center space-x-2 bg-red-50 px-3 py-1 rounded-full">
                            <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                            <span class="text-red-600 font-medium text-sm">18:42</span>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Progress Bar -->
        <div class="bg-white border-b">
            <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="py-2">
                    <div class="w-full bg-gray-200 rounded-full h-1">
                        <div class="bg-blue-600 h-1 rounded-full" style="width: 92%"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <main class="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
            <!-- Question Header -->
            <div class="mb-6">
                <div class="flex items-center space-x-3 mb-4">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"/>
                        </svg>
                        Speaking
                    </span>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                        B2 Level
                    </span>
                </div>
                <h2 class="text-xl font-semibold text-gray-900">Pregunta 23</h2>
            </div>

            <!-- Question Content -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-8">
                    <!-- Question Text -->
                    <div class="mb-8">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">
                            Describe tu ciudad natal
                        </h3>
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                            <p class="text-blue-900">
                                Habla durante <strong>60-90 segundos</strong> sobre tu ciudad natal. En tu respuesta, incluye:
                            </p>
                            <ul class="list-disc list-inside mt-3 text-blue-800 space-y-1">
                                <li>Dónde está ubicada y cómo es el clima</li>
                                <li>Los lugares más interesantes para visitar</li>
                                <li>Qué actividades se pueden hacer allí</li>
                                <li>Por qué recomendarías o no visitarla</li>
                            </ul>
                        </div>
                        
                        <!-- Preparation Time -->
                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-yellow-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"/>
                                </svg>
                                <div>
                                    <p class="text-yellow-800 font-medium">Tiempo de preparación: 30 segundos</p>
                                    <p class="text-yellow-700 text-sm">Usa este tiempo para organizar tus ideas antes de grabar</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recording Interface -->
                    <div class="space-y-6">
                        <!-- Preparation Timer -->
                        <div id="preparationSection" class="text-center">
                            <div class="w-24 h-24 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <span id="prepTimer" class="text-2xl font-bold text-yellow-600">30</span>
                            </div>
                            <h4 class="text-lg font-medium text-gray-900 mb-2">Tiempo de preparación</h4>
                            <p class="text-gray-600">Organiza tus ideas. La grabación comenzará automáticamente.</p>
                        </div>

                        <!-- Recording Section -->
                        <div id="recordingSection" class="text-center hidden">
                            <div class="w-32 h-32 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6 recording-pulse">
                                <svg class="w-12 h-12 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z" clip-rule="evenodd"/>
                                </svg>
                            </div>
                            
                            <!-- Recording Timer -->
                            <div class="mb-4">
                                <span id="recordTimer" class="text-3xl font-bold text-red-600">0:00</span>
                                <p class="text-gray-600 mt-1">Tiempo máximo: 90 segundos</p>
                            </div>
                            
                            <!-- Audio Level Indicator -->
                            <div class="mb-6">
                                <p class="text-sm text-gray-600 mb-2">Nivel de audio:</p>
                                <div class="audio-wave">
                                    <div class="wave-bar"></div>
                                    <div class="wave-bar"></div>
                                    <div class="wave-bar"></div>
                                    <div class="wave-bar"></div>
                                    <div class="wave-bar"></div>
                                    <div class="wave-bar"></div>
                                </div>
                            </div>
                            
                            <!-- Stop Recording Button -->
                            <button onclick="stopRecording()" 
                                    class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 012 0v6a1 1 0 11-2 0V7zm4 0a1 1 0 012 0v6a1 1 0 11-2 0V7z" clip-rule="evenodd"/>
                                </svg>
                                Detener Grabación
                            </button>
                        </div>

                        <!-- Playback Section -->
                        <div id="playbackSection" class="hidden">
                            <div class="bg-gray-50 border border-gray-200 rounded-lg p-6">
                                <h4 class="text-lg font-medium text-gray-900 mb-4">Tu respuesta grabada</h4>
                                
                                <!-- Audio Player -->
                                <div class="flex items-center space-x-4 mb-4">
                                    <button id="playbackBtn" onclick="togglePlayback()" 
                                            class="w-12 h-12 bg-blue-600 hover:bg-blue-700 rounded-full flex items-center justify-center transition-colors">
                                        <svg id="playbackIcon" class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"/>
                                        </svg>
                                    </button>
                                    <div class="flex-1">
                                        <div class="flex items-center justify-between text-sm text-gray-600 mb-1">
                                            <span id="playbackTime">0:00</span>
                                            <span id="totalTime">1:23</span>
                                        </div>
                                        <div class="w-full bg-gray-200 rounded-full h-2">
                                            <div id="playbackProgress" class="bg-blue-600 h-2 rounded-full" style="width: 0%"></div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Recording Info -->
                                <div class="flex items-center justify-between text-sm text-gray-600 mb-4">
                                    <span>Duración: 1:23</span>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Grabación completada
                                    </span>
                                </div>
                                
                                <!-- Action Buttons -->
                                <div class="flex space-x-3">
                                    <button onclick="reRecord()" 
                                            class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                                        </svg>
                                        Re-grabar (1 oportunidad)
                                    </button>
                                    <button onclick="confirmAnswer()" 
                                            class="inline-flex items-center px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                                        </svg>
                                        Confirmar Respuesta
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Technical Info -->
            <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-blue-800">
                            Consejos para una buena grabación
                        </h3>
                        <div class="mt-2 text-sm text-blue-700">
                            <ul class="list-disc list-inside space-y-1">
                                <li>Habla claramente y a un ritmo natural</li>
                                <li>Mantén una distancia adecuada del micrófono</li>
                                <li>Evita ruidos de fondo</li>
                                <li>Organiza tus ideas durante el tiempo de preparación</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Navigation -->
            <div class="mt-8 flex justify-between">
                <button onclick="window.location.href='17_exam_listening_question.html'"
                        class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                    </svg>
                    Anterior
                </button>

                <button id="nextBtn" disabled onclick="finishExam()"
                        class="inline-flex items-center px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-gray-400 cursor-not-allowed">
                    Finalizar Examen
                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                </button>
            </div>
        </main>
    </div>

    <script>
        let prepTime = 30;
        let recordTime = 0;
        let isRecording = false;
        let isPlaying = false;
        let hasRecorded = false;
        let canReRecord = true;

        // Start preparation timer
        const prepInterval = setInterval(() => {
            prepTime--;
            document.getElementById('prepTimer').textContent = prepTime;
            
            if (prepTime <= 0) {
                clearInterval(prepInterval);
                startRecording();
            }
        }, 1000);

        function startRecording() {
            document.getElementById('preparationSection').classList.add('hidden');
            document.getElementById('recordingSection').classList.remove('hidden');
            
            isRecording = true;
            const recordInterval = setInterval(() => {
                recordTime++;
                const minutes = Math.floor(recordTime / 60);
                const seconds = recordTime % 60;
                document.getElementById('recordTimer').textContent = 
                    `${minutes}:${seconds.toString().padStart(2, '0')}`;
                
                if (recordTime >= 90) {
                    clearInterval(recordInterval);
                    stopRecording();
                }
            }, 1000);
        }

        function stopRecording() {
            isRecording = false;
            hasRecorded = true;
            
            document.getElementById('recordingSection').classList.add('hidden');
            document.getElementById('playbackSection').classList.remove('hidden');
            
            // Update total time
            const minutes = Math.floor(recordTime / 60);
            const seconds = recordTime % 60;
            document.getElementById('totalTime').textContent = 
                `${minutes}:${seconds.toString().padStart(2, '0')}`;
        }

        function togglePlayback() {
            const btn = document.getElementById('playbackBtn');
            const icon = document.getElementById('playbackIcon');
            
            if (!isPlaying) {
                isPlaying = true;
                icon.innerHTML = '<path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 002 0V8a1 1 0 00-1-1z" clip-rule="evenodd"/>';
                // Simulate playback
                setTimeout(() => {
                    isPlaying = false;
                    icon.innerHTML = '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"/>';
                }, 3000);
            } else {
                isPlaying = false;
                icon.innerHTML = '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"/>';
            }
        }

        function reRecord() {
            if (canReRecord) {
                canReRecord = false;
                recordTime = 0;
                document.getElementById('playbackSection').classList.add('hidden');
                document.getElementById('recordingSection').classList.remove('hidden');
                startRecording();
            }
        }

        function confirmAnswer() {
            const nextBtn = document.getElementById('nextBtn');
            nextBtn.disabled = false;
            nextBtn.className = "inline-flex items-center px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500";

            // Show confirmation
            alert('Respuesta confirmada. Puedes finalizar el examen.');
        }

        function finishExam() {
            if (confirm('¿Estás seguro de que quieres finalizar el examen? No podrás hacer cambios después.')) {
                window.location.href = '21_results_dashboard.html';
            }
        }
    </script>
</body>
</html>
