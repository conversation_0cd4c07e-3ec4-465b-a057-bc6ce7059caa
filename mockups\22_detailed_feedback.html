<!DOCTYPE html>
<html lang="es" class="h-full bg-gray-50">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Feedback Detallado - Arroyo University</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
    </style>
</head>
<body class="h-full">
    <div class="min-h-full">
        <!-- Header -->
        <header class="bg-white shadow">
            <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-6">
                    <div class="flex items-center">
                        <button onclick="window.location.href='21_results_dashboard.html'" 
                                class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center hover:bg-blue-700 transition-colors mr-3">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                            </svg>
                        </button>
                        <div>
                            <h1 class="text-lg font-semibold text-gray-900">Feedback Detallado</h1>
                            <p class="text-sm text-gray-600">English B2 Assessment</p>
                        </div>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <button class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                            </svg>
                            Descargar PDF
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
            <!-- Overall Results -->
            <div class="bg-white shadow rounded-lg mb-8">
                <div class="px-6 py-6">
                    <div class="text-center mb-6">
                        <div class="text-4xl font-bold text-blue-600 mb-2">B2.1</div>
                        <div class="text-lg text-gray-900 mb-1">Nivel CEFR Alcanzado</div>
                        <div class="text-sm text-gray-600">78/100 puntos (78%)</div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div class="text-center p-4 bg-blue-50 rounded-lg">
                            <div class="text-xl font-bold text-blue-600">4.2/5</div>
                            <div class="text-sm text-blue-800">Writing</div>
                            <div class="text-xs text-blue-600">84%</div>
                        </div>
                        <div class="text-center p-4 bg-green-50 rounded-lg">
                            <div class="text-xl font-bold text-green-600">3.8/5</div>
                            <div class="text-sm text-green-800">Listening</div>
                            <div class="text-xs text-green-600">76%</div>
                        </div>
                        <div class="text-center p-4 bg-purple-50 rounded-lg">
                            <div class="text-xl font-bold text-purple-600">4.0/5</div>
                            <div class="text-sm text-purple-800">Speaking</div>
                            <div class="text-xs text-purple-600">80%</div>
                        </div>
                        <div class="text-center p-4 bg-yellow-50 rounded-lg">
                            <div class="text-xl font-bold text-yellow-600">3.9/5</div>
                            <div class="text-sm text-yellow-800">Reading</div>
                            <div class="text-xs text-yellow-600">78%</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Writing Feedback -->
            <div class="bg-white shadow rounded-lg mb-8">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"/>
                        </svg>
                        <h3 class="text-lg font-medium text-gray-900">Writing - 4.2/5 (84%)</h3>
                    </div>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                        <div class="text-center">
                            <div class="text-lg font-bold text-blue-600">4.5/5</div>
                            <div class="text-sm text-gray-600">Coherencia</div>
                        </div>
                        <div class="text-center">
                            <div class="text-lg font-bold text-blue-600">4.2/5</div>
                            <div class="text-sm text-gray-600">Gramática</div>
                        </div>
                        <div class="text-center">
                            <div class="text-lg font-bold text-blue-600">4.2/5</div>
                            <div class="text-sm text-gray-600">Vocabulario</div>
                        </div>
                        <div class="text-center">
                            <div class="text-lg font-bold text-blue-600">4.0/5</div>
                            <div class="text-sm text-gray-600">Estructura</div>
                        </div>
                    </div>
                    
                    <div class="space-y-4">
                        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                            <h4 class="text-sm font-medium text-green-900 mb-2">✓ Fortalezas</h4>
                            <ul class="text-sm text-green-800 space-y-1">
                                <li>• Excelente organización de ideas en párrafos claros</li>
                                <li>• Uso apropiado de conectores (además, por otro lado, en conclusión)</li>
                                <li>• Vocabulario variado y apropiado para el nivel B2</li>
                                <li>• Cumple con el requisito de palabras (142/150)</li>
                            </ul>
                        </div>
                        
                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                            <h4 class="text-sm font-medium text-yellow-900 mb-2">⚠ Áreas de mejora</h4>
                            <ul class="text-sm text-yellow-800 space-y-1">
                                <li>• Pequeños errores de preposición: "mejorado mucho mi técnica" → "mejorado mucho en mi técnica"</li>
                                <li>• Considera usar más estructuras complejas (condicionales, subjuntivo)</li>
                                <li>• Algunos ejemplos podrían ser más específicos</li>
                            </ul>
                        </div>
                        
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <h4 class="text-sm font-medium text-blue-900 mb-2">💡 Recomendaciones</h4>
                            <ul class="text-sm text-blue-800 space-y-1">
                                <li>• Practica el uso de preposiciones con verbos específicos</li>
                                <li>• Incorpora más estructuras gramaticales complejas</li>
                                <li>• Continúa desarrollando tu vocabulario temático</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Listening Feedback -->
            <div class="bg-white shadow rounded-lg mb-8">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728"/>
                        </svg>
                        <h3 class="text-lg font-medium text-gray-900">Listening - 3.8/5 (76%)</h3>
                    </div>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                        <div class="text-center">
                            <div class="text-lg font-bold text-green-600">4.0/5</div>
                            <div class="text-sm text-gray-600">Comprensión General</div>
                        </div>
                        <div class="text-center">
                            <div class="text-lg font-bold text-green-600">3.5/5</div>
                            <div class="text-sm text-gray-600">Detalles Específicos</div>
                        </div>
                        <div class="text-center">
                            <div class="text-lg font-bold text-green-600">4.0/5</div>
                            <div class="text-sm text-gray-600">Inferencias</div>
                        </div>
                    </div>
                    
                    <div class="space-y-4">
                        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                            <h4 class="text-sm font-medium text-green-900 mb-2">✓ Fortalezas</h4>
                            <ul class="text-sm text-green-800 space-y-1">
                                <li>• Buena comprensión del contexto general</li>
                                <li>• Capacidad para hacer inferencias apropiadas</li>
                                <li>• Identificación correcta de actitudes y opiniones</li>
                            </ul>
                        </div>
                        
                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                            <h4 class="text-sm font-medium text-yellow-900 mb-2">⚠ Áreas de mejora</h4>
                            <ul class="text-sm text-yellow-800 space-y-1">
                                <li>• Dificultad con números específicos y fechas</li>
                                <li>• Algunos detalles específicos no fueron captados</li>
                                <li>• Velocidad de procesamiento en conversaciones rápidas</li>
                            </ul>
                        </div>
                        
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <h4 class="text-sm font-medium text-blue-900 mb-2">💡 Recomendaciones</h4>
                            <ul class="text-sm text-blue-800 space-y-1">
                                <li>• Practica con audios que contengan información numérica</li>
                                <li>• Escucha podcasts y noticias para mejorar velocidad</li>
                                <li>• Trabaja en tomar notas mientras escuchas</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Speaking Feedback -->
            <div class="bg-white shadow rounded-lg mb-8">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-purple-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"/>
                        </svg>
                        <h3 class="text-lg font-medium text-gray-900">Speaking - 4.0/5 (80%)</h3>
                    </div>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                        <div class="text-center">
                            <div class="text-lg font-bold text-purple-600">4.2/5</div>
                            <div class="text-sm text-gray-600">Fluidez</div>
                        </div>
                        <div class="text-center">
                            <div class="text-lg font-bold text-purple-600">3.8/5</div>
                            <div class="text-sm text-gray-600">Pronunciación</div>
                        </div>
                        <div class="text-center">
                            <div class="text-lg font-bold text-purple-600">4.0/5</div>
                            <div class="text-sm text-gray-600">Vocabulario</div>
                        </div>
                        <div class="text-center">
                            <div class="text-lg font-bold text-purple-600">4.0/5</div>
                            <div class="text-sm text-gray-600">Gramática</div>
                        </div>
                    </div>
                    
                    <div class="space-y-4">
                        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                            <h4 class="text-sm font-medium text-green-900 mb-2">✓ Fortalezas</h4>
                            <ul class="text-sm text-green-800 space-y-1">
                                <li>• Discurso fluido con pocas pausas</li>
                                <li>• Buena organización de ideas</li>
                                <li>• Uso apropiado de conectores hablados</li>
                                <li>• Respuesta completa dentro del tiempo asignado</li>
                            </ul>
                        </div>
                        
                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                            <h4 class="text-sm font-medium text-yellow-900 mb-2">⚠ Áreas de mejora</h4>
                            <ul class="text-sm text-yellow-800 space-y-1">
                                <li>• Algunos sonidos específicos (/θ/, /ð/) necesitan práctica</li>
                                <li>• Entonación en preguntas indirectas</li>
                                <li>• Uso ocasional de muletillas en español</li>
                            </ul>
                        </div>
                        
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <h4 class="text-sm font-medium text-blue-900 mb-2">💡 Recomendaciones</h4>
                            <ul class="text-sm text-blue-800 space-y-1">
                                <li>• Practica sonidos específicos con trabalenguas</li>
                                <li>• Graba tu voz y compara con hablantes nativos</li>
                                <li>• Practica presentaciones de 2-3 minutos regularmente</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Overall Recommendations -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
                <h3 class="text-lg font-medium text-blue-900 mb-4">Recomendaciones Generales</h3>
                <div class="space-y-3 text-blue-800">
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                        </svg>
                        <p><strong>¡Felicitaciones!</strong> Has alcanzado el nivel B2.1, lo que demuestra un dominio sólido del inglés intermedio alto.</p>
                    </div>
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                        </svg>
                        <p>Para alcanzar B2.2, enfócate en mejorar la comprensión de detalles específicos en listening y perfeccionar la pronunciación.</p>
                    </div>
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                        </svg>
                        <p>Continúa practicando con material auténtico: noticias, podcasts, y conversaciones reales.</p>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <button onclick="window.location.href='21_results_dashboard.html'" 
                        class="inline-flex items-center px-6 py-3 border border-gray-300 rounded-md text-base font-medium text-gray-700 bg-white hover:bg-gray-50">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                    </svg>
                    Volver a Resultados
                </button>
                
                <button onclick="window.location.href='06_student_dashboard.html'" 
                        class="inline-flex items-center px-6 py-3 border border-transparent rounded-md text-base font-medium text-white bg-blue-600 hover:bg-blue-700">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"/>
                    </svg>
                    Ir al Dashboard
                </button>
            </div>
        </main>
    </div>
</body>
</html>
