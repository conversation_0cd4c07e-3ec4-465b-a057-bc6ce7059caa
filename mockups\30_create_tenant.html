<!DOCTYPE html>
<html lang="es" class="h-full bg-gray-50">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crear Nuevo Tenant - Arroyo University</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
    </style>
</head>
<body class="h-full">
    <div class="min-h-full">
        <!-- Navigation -->
        <nav class="bg-white shadow">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <button onclick="window.location.href='07_system_admin_dashboard.html'" 
                                class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center hover:bg-blue-700 transition-colors">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.84L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3z"/>
                            </svg>
                        </button>
                        <h1 class="ml-3 text-xl font-semibold text-gray-900">Arroyo University</h1>
                        <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            System Owner
                        </span>
                        <nav class="ml-8 flex space-x-4">
                            <a href="07_system_admin_dashboard.html" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">Dashboard</a>
                            <a href="#" class="bg-blue-100 text-blue-700 px-3 py-2 rounded-md text-sm font-medium">Crear Tenant</a>
                            <a href="31_tenant_management.html" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">Gestionar</a>
                        </nav>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                            <span class="text-sm font-medium text-white">SO</span>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="mb-8">
                <h2 class="text-2xl font-bold text-gray-900">Crear Nuevo Tenant</h2>
                <p class="mt-1 text-gray-600">Configura una nueva organización en la plataforma</p>
            </div>

            <!-- Creation Form -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-8">
                    <form class="space-y-8">
                        <!-- Basic Information -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Información Básica</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="org-name" class="block text-sm font-medium text-gray-700 mb-2">
                                        Nombre de la Organización *
                                    </label>
                                    <input type="text" id="org-name" required
                                           placeholder="Universidad Central"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                
                                <div>
                                    <label for="subdomain" class="block text-sm font-medium text-gray-700 mb-2">
                                        Subdominio *
                                    </label>
                                    <div class="flex">
                                        <input type="text" id="subdomain" required
                                               placeholder="universidad-central"
                                               class="flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                        <span class="inline-flex items-center px-3 py-2 border border-l-0 border-gray-300 bg-gray-50 text-gray-500 text-sm rounded-r-md">
                                            .arroyo.app
                                        </span>
                                    </div>
                                    <p class="mt-1 text-xs text-gray-500">Solo letras, números y guiones. Mínimo 3 caracteres.</p>
                                </div>
                                
                                <div>
                                    <label for="admin-email" class="block text-sm font-medium text-gray-700 mb-2">
                                        Email del Administrador *
                                    </label>
                                    <input type="email" id="admin-email" required
                                           placeholder="<EMAIL>"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                
                                <div>
                                    <label for="admin-name" class="block text-sm font-medium text-gray-700 mb-2">
                                        Nombre del Administrador *
                                    </label>
                                    <input type="text" id="admin-name" required
                                           placeholder="Dr. María González"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                </div>
                            </div>
                        </div>

                        <!-- Plan Selection -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Selección de Plan</h3>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div class="relative">
                                    <input type="radio" name="plan" value="starter" id="starter" class="sr-only" checked>
                                    <label for="starter" class="flex flex-col p-6 border-2 border-blue-200 bg-blue-50 rounded-lg cursor-pointer hover:bg-blue-100 transition-colors">
                                        <div class="flex items-center justify-between mb-3">
                                            <span class="text-lg font-semibold text-blue-900">Starter</span>
                                            <span class="text-2xl font-bold text-blue-900">$99</span>
                                        </div>
                                        <span class="text-sm text-blue-700 mb-3">/mes</span>
                                        <ul class="text-sm text-blue-800 space-y-1">
                                            <li>• Hasta 100 usuarios</li>
                                            <li>• 1,000 tokens IA/mes</li>
                                            <li>• Soporte email</li>
                                            <li>• Backups diarios</li>
                                        </ul>
                                    </label>
                                </div>
                                
                                <div class="relative">
                                    <input type="radio" name="plan" value="professional" id="professional" class="sr-only">
                                    <label for="professional" class="flex flex-col p-6 border-2 border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                                        <div class="flex items-center justify-between mb-3">
                                            <span class="text-lg font-semibold text-gray-900">Professional</span>
                                            <span class="text-2xl font-bold text-gray-900">$299</span>
                                        </div>
                                        <span class="text-sm text-gray-600 mb-3">/mes</span>
                                        <ul class="text-sm text-gray-700 space-y-1">
                                            <li>• Hasta 500 usuarios</li>
                                            <li>• 5,000 tokens IA/mes</li>
                                            <li>• Soporte prioritario</li>
                                            <li>• SSO incluido</li>
                                        </ul>
                                    </label>
                                </div>
                                
                                <div class="relative">
                                    <input type="radio" name="plan" value="enterprise" id="enterprise" class="sr-only">
                                    <label for="enterprise" class="flex flex-col p-6 border-2 border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                                        <div class="flex items-center justify-between mb-3">
                                            <span class="text-lg font-semibold text-gray-900">Enterprise</span>
                                            <span class="text-2xl font-bold text-gray-900">$999</span>
                                        </div>
                                        <span class="text-sm text-gray-600 mb-3">/mes</span>
                                        <ul class="text-sm text-gray-700 space-y-1">
                                            <li>• Usuarios ilimitados</li>
                                            <li>• 25,000 tokens IA/mes</li>
                                            <li>• Soporte 24/7</li>
                                            <li>• Personalización completa</li>
                                        </ul>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Configuration Options -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Configuración Inicial</h3>
                            <div class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-900">Enviar email de bienvenida</h4>
                                        <p class="text-sm text-gray-500">Enviar instrucciones de configuración al administrador</p>
                                    </div>
                                    <button type="button" class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent bg-blue-600 transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                        <span class="translate-x-5 inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"></span>
                                    </button>
                                </div>
                                
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-900">Configuración automática</h4>
                                        <p class="text-sm text-gray-500">Aplicar configuración predeterminada recomendada</p>
                                    </div>
                                    <button type="button" class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent bg-blue-600 transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                        <span class="translate-x-5 inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"></span>
                                    </button>
                                </div>
                                
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-900">Período de prueba</h4>
                                        <p class="text-sm text-gray-500">Otorgar 30 días de prueba gratuita</p>
                                    </div>
                                    <button type="button" class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent bg-gray-200 transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                        <span class="translate-x-0 inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"></span>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Additional Notes -->
                        <div>
                            <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">
                                Notas adicionales
                            </label>
                            <textarea id="notes" rows="3" 
                                      placeholder="Información adicional sobre el tenant, requisitos especiales, etc."
                                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"></textarea>
                        </div>

                        <!-- Estimated Costs -->
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <h4 class="text-sm font-medium text-blue-900 mb-2">Resumen de Costos</h4>
                            <div class="space-y-1 text-sm text-blue-800">
                                <div class="flex justify-between">
                                    <span>Plan Starter (mensual):</span>
                                    <span>$99.00</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>Configuración inicial:</span>
                                    <span>$0.00</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>Período de prueba:</span>
                                    <span class="text-green-600">-$0.00</span>
                                </div>
                                <div class="border-t border-blue-200 pt-1 mt-2">
                                    <div class="flex justify-between font-medium">
                                        <span>Total primer mes:</span>
                                        <span>$99.00</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex justify-between pt-6 border-t">
                            <button type="button" onclick="window.location.href='07_system_admin_dashboard.html'" 
                                    class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 font-medium">
                                Cancelar
                            </button>
                            <button type="button" onclick="createTenant()" 
                                    class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 font-medium">
                                Crear Tenant
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </main>
    </div>

    <script>
        function createTenant() {
            // Show loading state
            const btn = event.target;
            btn.disabled = true;
            btn.innerHTML = 'Creando...';
            
            // Simulate tenant creation process
            setTimeout(() => {
                alert('¡Tenant creado exitosamente!\n\nSubdominio: universidad-central.arroyo.app\nEmail de bienvenida enviado al administrador.');
                window.location.href = '31_tenant_management.html';
            }, 2000);
        }

        // Plan selection handling
        document.querySelectorAll('input[name="plan"]').forEach(radio => {
            radio.addEventListener('change', function() {
                // Remove active state from all plans
                document.querySelectorAll('label[for^="starter"], label[for^="professional"], label[for^="enterprise"]').forEach(label => {
                    label.className = label.className.replace('border-blue-200 bg-blue-50', 'border-gray-200');
                    label.className = label.className.replace('text-blue-900', 'text-gray-900');
                    label.className = label.className.replace('text-blue-700', 'text-gray-600');
                    label.className = label.className.replace('text-blue-800', 'text-gray-700');
                });
                
                // Add active state to selected plan
                const selectedLabel = document.querySelector(`label[for="${this.value}"]`);
                selectedLabel.className = selectedLabel.className.replace('border-gray-200', 'border-blue-200 bg-blue-50');
                selectedLabel.className = selectedLabel.className.replace('text-gray-900', 'text-blue-900');
                selectedLabel.className = selectedLabel.className.replace('text-gray-600', 'text-blue-700');
                selectedLabel.className = selectedLabel.className.replace('text-gray-700', 'text-blue-800');
            });
        });
    </script>
</body>
</html>
