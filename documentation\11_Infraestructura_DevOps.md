# Infraestructura y DevOps - Arroyo University

## Introducción

Este documento describe la infraestructura cloud, estrategias de deployment, CI/CD pipelines y operaciones de Arroyo University. La arquitectura está diseñada para alta disponibilidad, escalabilidad automática y operaciones eficientes.

---

## 1. Arquitectura de Infraestructura

### 1.1 Cloud Provider: Microsoft Azure

| Servicio | Propósito | Configuración | Región |
|----------|-----------|---------------|--------|
| **Azure Kubernetes Service (AKS)** | Container orchestration | 3-node cluster, auto-scaling | East US, West Europe |
| **Azure Database for PostgreSQL** | Primary database | Flexible Server, HA enabled | East US (primary) |
| **Azure Blob Storage** | Object storage | Hot tier, geo-redundant | Global |
| **Azure CDN** | Content delivery | Premium tier | Global |
| **Azure Key Vault** | Secrets management | Standard tier | East US, West Europe |
| **Azure Monitor** | Observability | Log Analytics workspace | East US |

### 1.2 Arquitectura Multi-Region

```
┌─────────────────┐    ┌─────────────────┐
│   East US       │    │  West Europe    │
│   (Primary)     │    │   (Secondary)   │
├─────────────────┤    ├─────────────────┤
│ AKS Cluster     │    │ AKS Cluster     │
│ PostgreSQL      │    │ Read Replica    │
│ Key Vault       │    │ Key Vault       │
│ Storage Account │    │ Storage Account │
└─────────────────┘    └─────────────────┘
         │                       │
         └───────────────────────┘
                   │
         ┌─────────────────┐
         │  Azure CDN      │
         │  (Global)       │
         └─────────────────┘
```

### 1.3 Networking

| Componente | Configuración | Seguridad |
|------------|---------------|-----------|
| **Virtual Network** | 10.0.0.0/16 | Network Security Groups |
| **Subnets** | AKS: ********/24, DB: ********/24 | Private endpoints |
| **Load Balancer** | Azure Application Gateway | WAF enabled |
| **DNS** | Azure DNS + Custom domain | SSL/TLS termination |

---

## 2. Containerización y Orquestación

### 2.1 Docker Configuration

#### Dockerfile (Core API)
```dockerfile
FROM python:3.11-slim

WORKDIR /app

# Install dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application
COPY . .

# Security: non-root user
RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app
USER appuser

EXPOSE 8000

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

#### Dockerfile (Frontend)
```dockerfile
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### 2.2 Kubernetes Manifests

#### Deployment (Core API)
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: core-api
  namespace: arroyo-prod
spec:
  replicas: 3
  selector:
    matchLabels:
      app: core-api
  template:
    metadata:
      labels:
        app: core-api
    spec:
      containers:
      - name: core-api
        image: arroyoregistry.azurecr.io/core-api:latest
        ports:
        - containerPort: 8000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: connection-string
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
```

#### Service & Ingress
```yaml
apiVersion: v1
kind: Service
metadata:
  name: core-api-service
spec:
  selector:
    app: core-api
  ports:
  - port: 80
    targetPort: 8000
  type: ClusterIP

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: core-api-ingress
  annotations:
    kubernetes.io/ingress.class: azure/application-gateway
    appgw.ingress.kubernetes.io/ssl-redirect: "true"
spec:
  tls:
  - hosts:
    - api.arroyo.app
    secretName: tls-secret
  rules:
  - host: api.arroyo.app
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: core-api-service
            port:
              number: 80
```

### 2.3 Auto-scaling

#### Horizontal Pod Autoscaler
```yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: core-api-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: core-api
  minReplicas: 3
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

---

## 3. CI/CD Pipeline

### 3.1 GitHub Actions Workflow

```yaml
name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

env:
  REGISTRY: arroyoregistry.azurecr.io
  IMAGE_NAME: core-api

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install -r requirements-dev.txt
    
    - name: Run tests
      run: |
        pytest --cov=. --cov-report=xml
        
    - name: Security scan
      run: |
        bandit -r . -f json -o bandit-report.json
        
    - name: Upload coverage
      uses: codecov/codecov-action@v3

  build:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Login to ACR
      uses: azure/docker-login@v1
      with:
        login-server: ${{ env.REGISTRY }}
        username: ${{ secrets.ACR_USERNAME }}
        password: ${{ secrets.ACR_PASSWORD }}
    
    - name: Build and push
      run: |
        docker build -t ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }} .
        docker push ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }}

  deploy:
    needs: build
    runs-on: ubuntu-latest
    environment: production
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Azure Login
      uses: azure/login@v1
      with:
        creds: ${{ secrets.AZURE_CREDENTIALS }}
    
    - name: Deploy to AKS
      uses: azure/k8s-deploy@v1
      with:
        manifests: |
          k8s/deployment.yaml
          k8s/service.yaml
        images: |
          ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }}
        kubectl-version: 'latest'
```

### 3.2 Deployment Strategies

#### Blue-Green Deployment
```bash
#!/bin/bash
# Blue-Green deployment script

NEW_VERSION=$1
CURRENT_COLOR=$(kubectl get service core-api-service -o jsonpath='{.spec.selector.version}')

if [ "$CURRENT_COLOR" = "blue" ]; then
    NEW_COLOR="green"
else
    NEW_COLOR="blue"
fi

# Deploy new version
kubectl set image deployment/core-api-$NEW_COLOR core-api=$REGISTRY/core-api:$NEW_VERSION

# Wait for rollout
kubectl rollout status deployment/core-api-$NEW_COLOR

# Health check
kubectl exec deployment/core-api-$NEW_COLOR -- curl -f http://localhost:8000/health

# Switch traffic
kubectl patch service core-api-service -p '{"spec":{"selector":{"version":"'$NEW_COLOR'"}}}'

echo "Deployment completed. Traffic switched to $NEW_COLOR"
```

#### Canary Deployment
```yaml
apiVersion: argoproj.io/v1alpha1
kind: Rollout
metadata:
  name: core-api-rollout
spec:
  replicas: 10
  strategy:
    canary:
      steps:
      - setWeight: 10
      - pause: {duration: 5m}
      - setWeight: 25
      - pause: {duration: 10m}
      - setWeight: 50
      - pause: {duration: 15m}
      - setWeight: 100
  selector:
    matchLabels:
      app: core-api
  template:
    metadata:
      labels:
        app: core-api
    spec:
      containers:
      - name: core-api
        image: arroyoregistry.azurecr.io/core-api:latest
```

---

## 4. Infrastructure as Code (Terraform)

### 4.1 Main Configuration

```hcl
terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~>3.0"
    }
  }
  
  backend "azurerm" {
    resource_group_name  = "terraform-state-rg"
    storage_account_name = "terraformstate"
    container_name       = "tfstate"
    key                  = "arroyo.terraform.tfstate"
  }
}

provider "azurerm" {
  features {}
}

# Resource Group
resource "azurerm_resource_group" "main" {
  name     = "arroyo-${var.environment}-rg"
  location = var.location
  
  tags = {
    Environment = var.environment
    Project     = "arroyo-university"
  }
}

# AKS Cluster
resource "azurerm_kubernetes_cluster" "main" {
  name                = "arroyo-${var.environment}-aks"
  location            = azurerm_resource_group.main.location
  resource_group_name = azurerm_resource_group.main.name
  dns_prefix          = "arroyo-${var.environment}"

  default_node_pool {
    name       = "default"
    node_count = 3
    vm_size    = "Standard_D2_v2"
    
    enable_auto_scaling = true
    min_count          = 3
    max_count          = 10
  }

  identity {
    type = "SystemAssigned"
  }

  network_profile {
    network_plugin = "azure"
  }
}

# PostgreSQL Database
resource "azurerm_postgresql_flexible_server" "main" {
  name                   = "arroyo-${var.environment}-db"
  resource_group_name    = azurerm_resource_group.main.name
  location              = azurerm_resource_group.main.location
  version               = "15"
  administrator_login    = var.db_admin_username
  administrator_password = var.db_admin_password
  
  storage_mb = 32768
  sku_name   = "GP_Standard_D2s_v3"
  
  high_availability {
    mode = "ZoneRedundant"
  }
  
  backup_retention_days = 30
}
```

### 4.2 Modules Structure

```
terraform/
├── modules/
│   ├── aks/
│   │   ├── main.tf
│   │   ├── variables.tf
│   │   └── outputs.tf
│   ├── database/
│   │   ├── main.tf
│   │   ├── variables.tf
│   │   └── outputs.tf
│   └── storage/
│       ├── main.tf
│       ├── variables.tf
│       └── outputs.tf
├── environments/
│   ├── dev/
│   │   ├── main.tf
│   │   └── terraform.tfvars
│   ├── staging/
│   │   ├── main.tf
│   │   └── terraform.tfvars
│   └── prod/
│       ├── main.tf
│       └── terraform.tfvars
└── shared/
    ├── variables.tf
    └── outputs.tf
```

---

## 5. Monitoring y Observabilidad

### 5.1 Prometheus Configuration

```yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

scrape_configs:
  - job_name: 'kubernetes-pods'
    kubernetes_sd_configs:
    - role: pod
    relabel_configs:
    - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
      action: keep
      regex: true

alerting:
  alertmanagers:
  - static_configs:
    - targets:
      - alertmanager:9093
```

### 5.2 Grafana Dashboards

```json
{
  "dashboard": {
    "title": "Arroyo University - Application Metrics",
    "panels": [
      {
        "title": "API Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          }
        ]
      },
      {
        "title": "Active Users",
        "type": "stat",
        "targets": [
          {
            "expr": "sum(active_sessions)",
            "legendFormat": "Active Sessions"
          }
        ]
      }
    ]
  }
}
```

### 5.3 Alert Rules

```yaml
groups:
- name: arroyo.rules
  rules:
  - alert: HighErrorRate
    expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "High error rate detected"
      
  - alert: DatabaseConnectionHigh
    expr: pg_stat_activity_count > 80
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "Database connection count is high"
```

---

## 6. Security y Compliance

### 6.1 Security Scanning

```yaml
# Security scan in CI/CD
- name: Container Security Scan
  uses: azure/container-scan@v0
  with:
    image-name: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }}
    severity-threshold: HIGH

- name: Infrastructure Security Scan
  run: |
    checkov -d . --framework terraform
    tfsec .
```

### 6.2 Secrets Management

```yaml
apiVersion: v1
kind: Secret
metadata:
  name: app-secrets
type: Opaque
data:
  database-url: <base64-encoded-value>
  openai-api-key: <base64-encoded-value>
  jwt-secret: <base64-encoded-value>
```

### 6.3 Network Policies

```yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: deny-all
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress

---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-core-api
spec:
  podSelector:
    matchLabels:
      app: core-api
  policyTypes:
  - Ingress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: frontend
    ports:
    - protocol: TCP
      port: 8000
```

---

## 7. Backup y Disaster Recovery

### 7.1 Database Backup Strategy

```bash
#!/bin/bash
# Automated backup script

BACKUP_NAME="arroyo-backup-$(date +%Y%m%d_%H%M%S)"
RETENTION_DAYS=30

# Create backup
az postgres flexible-server backup create \
  --resource-group $RESOURCE_GROUP \
  --server-name $SERVER_NAME \
  --backup-name $BACKUP_NAME

# Cleanup old backups
az postgres flexible-server backup list \
  --resource-group $RESOURCE_GROUP \
  --server-name $SERVER_NAME \
  --query "[?createdTime<'$(date -d "$RETENTION_DAYS days ago" -Iseconds)'].name" \
  --output tsv | xargs -I {} az postgres flexible-server backup delete \
  --resource-group $RESOURCE_GROUP \
  --server-name $SERVER_NAME \
  --backup-name {}
```

### 7.2 Application State Backup

```yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  name: app-backup
spec:
  schedule: "0 2 * * *"
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: backup
            image: arroyoregistry.azurecr.io/backup-tool:latest
            command:
            - /bin/sh
            - -c
            - |
              pg_dump $DATABASE_URL | gzip > /backup/app-$(date +%Y%m%d).sql.gz
              az storage blob upload --file /backup/app-$(date +%Y%m%d).sql.gz --container backups
          restartPolicy: OnFailure
```

---

## 8. Cost Optimization

### 8.1 Resource Right-sizing

| Recurso | Configuración Actual | Optimización | Ahorro Estimado |
|---------|---------------------|--------------|-----------------|
| **AKS Nodes** | Standard_D2_v2 | Spot instances para dev | 60% |
| **Database** | GP_Standard_D2s_v3 | Burstable para staging | 40% |
| **Storage** | Premium SSD | Standard SSD para logs | 50% |

### 8.2 Auto-shutdown Policies

```bash
# Auto-shutdown for non-production environments
az vm auto-shutdown \
  --resource-group $RESOURCE_GROUP \
  --name $VM_NAME \
  --time 1900 \
  --email $NOTIFICATION_EMAIL
```

---

## Conclusión

Esta infraestructura proporciona una base sólida, escalable y segura para Arroyo University. La combinación de tecnologías cloud-native, automatización completa y observabilidad robusta asegura operaciones eficientes y confiables mientras mantiene costos optimizados y cumplimiento de seguridad.
