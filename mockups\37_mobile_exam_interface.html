<!DOCTYPE html>
<html lang="es" class="h-full bg-gray-50">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Examen Móvil - Arroyo University</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .mobile-container {
            max-width: 375px;
            margin: 0 auto;
            min-height: 100vh;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body class="h-full bg-gray-200">
    <div class="mobile-container">
        <!-- Mobile Header -->
        <header class="bg-blue-600 text-white px-4 py-3">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <button onclick="showExitModal()" class="p-1">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                        </svg>
                    </button>
                    <div>
                        <h1 class="text-sm font-semibold">English B2 Assessment</h1>
                        <p class="text-xs opacity-75">Pregunta 15 de 25</p>
                    </div>
                </div>
                
                <div class="text-right">
                    <div class="text-lg font-bold" id="mobileTimer">01:23:45</div>
                    <div class="text-xs opacity-75">Tiempo restante</div>
                </div>
            </div>
            
            <!-- Progress Bar -->
            <div class="mt-3">
                <div class="w-full bg-blue-500 rounded-full h-1">
                    <div class="bg-white h-1 rounded-full transition-all duration-300" style="width: 60%"></div>
                </div>
            </div>
        </header>

        <!-- Question Content -->
        <main class="p-4 pb-20">
            <!-- Question Type Badge -->
            <div class="mb-4">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"/>
                    </svg>
                    Speaking
                </span>
            </div>

            <!-- Question -->
            <div class="mb-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-3">
                    Describe tu ciudad natal
                </h2>
                <p class="text-gray-600 text-sm leading-relaxed">
                    Habla durante 60-90 segundos sobre tu ciudad natal. En tu respuesta, incluye:
                </p>
                <ul class="mt-2 text-sm text-gray-600 space-y-1">
                    <li>• Ubicación geográfica</li>
                    <li>• Clima típico</li>
                    <li>• Lugares de interés</li>
                    <li>• Qué recomendarías a un visitante</li>
                </ul>
            </div>

            <!-- Preparation Timer -->
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-sm font-medium text-yellow-900">Tiempo de preparación</h3>
                        <p class="text-xs text-yellow-700">Piensa en tu respuesta</p>
                    </div>
                    <div class="text-2xl font-bold text-yellow-600" id="prepTimer">00:30</div>
                </div>
                <div class="mt-2">
                    <div class="w-full bg-yellow-200 rounded-full h-2">
                        <div class="bg-yellow-500 h-2 rounded-full transition-all duration-1000" id="prepProgress" style="width: 100%"></div>
                    </div>
                </div>
            </div>

            <!-- Recording Interface -->
            <div class="bg-white border border-gray-200 rounded-lg p-6 text-center">
                <div class="mb-4">
                    <div class="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-3" id="recordButton">
                        <svg class="w-8 h-8 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900">Listo para grabar</h3>
                    <p class="text-sm text-gray-500">Toca el micrófono para comenzar</p>
                </div>

                <!-- Recording Status -->
                <div id="recordingStatus" class="hidden">
                    <div class="flex items-center justify-center space-x-2 mb-3">
                        <div class="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                        <span class="text-red-600 font-medium">Grabando...</span>
                    </div>
                    <div class="text-2xl font-bold text-gray-900 mb-2" id="recordingTimer">00:00</div>
                    <div class="text-sm text-gray-500">Tiempo objetivo: 60-90 segundos</div>
                </div>

                <!-- Audio Waveform Visualization -->
                <div id="waveform" class="hidden mt-4">
                    <div class="flex items-end justify-center space-x-1 h-12">
                        <div class="w-1 bg-blue-400 rounded-full animate-pulse" style="height: 20%"></div>
                        <div class="w-1 bg-blue-400 rounded-full animate-pulse" style="height: 60%; animation-delay: 0.1s"></div>
                        <div class="w-1 bg-blue-400 rounded-full animate-pulse" style="height: 40%; animation-delay: 0.2s"></div>
                        <div class="w-1 bg-blue-400 rounded-full animate-pulse" style="height: 80%; animation-delay: 0.3s"></div>
                        <div class="w-1 bg-blue-400 rounded-full animate-pulse" style="height: 30%; animation-delay: 0.4s"></div>
                        <div class="w-1 bg-blue-400 rounded-full animate-pulse" style="height: 70%; animation-delay: 0.5s"></div>
                        <div class="w-1 bg-blue-400 rounded-full animate-pulse" style="height: 50%; animation-delay: 0.6s"></div>
                        <div class="w-1 bg-blue-400 rounded-full animate-pulse" style="height: 90%; animation-delay: 0.7s"></div>
                    </div>
                </div>
            </div>

            <!-- Recording Complete -->
            <div id="recordingComplete" class="hidden bg-green-50 border border-green-200 rounded-lg p-4 mt-4">
                <div class="flex items-center">
                    <svg class="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    </svg>
                    <div>
                        <h4 class="text-sm font-medium text-green-900">Grabación completada</h4>
                        <p class="text-xs text-green-700">Duración: 75 segundos</p>
                    </div>
                </div>
                
                <!-- Playback Controls -->
                <div class="mt-3 flex items-center space-x-3">
                    <button onclick="playRecording()" class="flex items-center space-x-1 text-green-700 hover:text-green-600">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"/>
                        </svg>
                        <span class="text-sm">Reproducir</span>
                    </button>
                    <button onclick="reRecord()" class="flex items-center space-x-1 text-green-700 hover:text-green-600">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                        </svg>
                        <span class="text-sm">Grabar de nuevo</span>
                    </button>
                </div>
            </div>

            <!-- Help Text -->
            <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 class="text-sm font-medium text-blue-900 mb-2">💡 Consejos</h4>
                <ul class="text-xs text-blue-800 space-y-1">
                    <li>• Habla con claridad y a un ritmo natural</li>
                    <li>• Organiza tus ideas antes de comenzar</li>
                    <li>• Puedes grabar hasta 3 veces si es necesario</li>
                    <li>• Asegúrate de estar en un lugar silencioso</li>
                </ul>
            </div>
        </main>

        <!-- Fixed Bottom Navigation -->
        <div class="fixed bottom-0 left-1/2 transform -translate-x-1/2 w-full max-w-sm bg-white border-t border-gray-200 p-4">
            <div class="flex items-center justify-between">
                <button onclick="previousQuestion()" class="flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-gray-800">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                    </svg>
                    <span class="text-sm">Anterior</span>
                </button>
                
                <div class="text-center">
                    <div class="text-xs text-gray-500">15 / 25</div>
                    <div class="w-16 bg-gray-200 rounded-full h-1 mt-1">
                        <div class="bg-blue-600 h-1 rounded-full" style="width: 60%"></div>
                    </div>
                </div>
                
                <button onclick="nextQuestion()" class="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50" id="nextButton" disabled>
                    <span class="text-sm">Siguiente</span>
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                </button>
            </div>
        </div>

        <!-- Exit Confirmation Modal -->
        <div id="exitModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 hidden">
            <div class="bg-white rounded-lg p-6 w-full max-w-sm">
                <h3 class="text-lg font-semibold text-gray-900 mb-2">¿Salir del examen?</h3>
                <p class="text-gray-600 text-sm mb-4">
                    Si sales ahora, perderás todo el progreso. ¿Estás seguro?
                </p>
                <div class="flex space-x-3">
                    <button onclick="hideExitModal()" class="flex-1 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                        Cancelar
                    </button>
                    <button onclick="exitExam()" class="flex-1 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">
                        Salir
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let isRecording = false;
        let recordingTime = 0;
        let prepTime = 30;
        let examTime = 5025; // 1:23:45 in seconds
        let recordingInterval;
        let prepInterval;
        let examInterval;

        // Start preparation timer
        function startPrepTimer() {
            prepInterval = setInterval(() => {
                prepTime--;
                const minutes = Math.floor(prepTime / 60);
                const seconds = prepTime % 60;
                document.getElementById('prepTimer').textContent = 
                    `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                
                const progress = (prepTime / 30) * 100;
                document.getElementById('prepProgress').style.width = progress + '%';
                
                if (prepTime <= 0) {
                    clearInterval(prepInterval);
                    enableRecording();
                }
            }, 1000);
        }

        // Start exam timer
        function startExamTimer() {
            examInterval = setInterval(() => {
                examTime--;
                const hours = Math.floor(examTime / 3600);
                const minutes = Math.floor((examTime % 3600) / 60);
                const seconds = examTime % 60;
                
                document.getElementById('mobileTimer').textContent = 
                    `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                
                if (examTime <= 0) {
                    clearInterval(examInterval);
                    alert('Tiempo agotado');
                }
            }, 1000);
        }

        function enableRecording() {
            document.getElementById('recordButton').onclick = startRecording;
            document.getElementById('recordButton').classList.add('cursor-pointer', 'hover:bg-red-200');
        }

        function startRecording() {
            if (isRecording) {
                stopRecording();
                return;
            }

            isRecording = true;
            recordingTime = 0;
            
            // Update UI
            document.getElementById('recordingStatus').classList.remove('hidden');
            document.getElementById('waveform').classList.remove('hidden');
            document.querySelector('#recordButton svg').innerHTML = `
                <rect x="6" y="6" width="8" height="8" fill="currentColor"/>
            `;
            
            // Start recording timer
            recordingInterval = setInterval(() => {
                recordingTime++;
                const minutes = Math.floor(recordingTime / 60);
                const seconds = recordingTime % 60;
                document.getElementById('recordingTimer').textContent = 
                    `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                
                // Auto-stop at 2 minutes
                if (recordingTime >= 120) {
                    stopRecording();
                }
            }, 1000);
        }

        function stopRecording() {
            isRecording = false;
            clearInterval(recordingInterval);
            
            // Hide recording UI
            document.getElementById('recordingStatus').classList.add('hidden');
            document.getElementById('waveform').classList.add('hidden');
            
            // Show completion UI
            document.getElementById('recordingComplete').classList.remove('hidden');
            
            // Enable next button
            document.getElementById('nextButton').disabled = false;
            document.getElementById('nextButton').classList.remove('opacity-50');
        }

        function playRecording() {
            alert('Reproduciendo grabación...');
        }

        function reRecord() {
            document.getElementById('recordingComplete').classList.add('hidden');
            document.getElementById('nextButton').disabled = true;
            document.getElementById('nextButton').classList.add('opacity-50');
            
            // Reset record button
            document.querySelector('#recordButton svg').innerHTML = `
                <path fill-rule="evenodd" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z" clip-rule="evenodd"/>
            `;
        }

        function previousQuestion() {
            alert('Navegando a pregunta anterior...');
        }

        function nextQuestion() {
            if (document.getElementById('nextButton').disabled) {
                alert('Completa la grabación antes de continuar');
                return;
            }
            alert('Navegando a siguiente pregunta...');
        }

        function showExitModal() {
            document.getElementById('exitModal').classList.remove('hidden');
        }

        function hideExitModal() {
            document.getElementById('exitModal').classList.add('hidden');
        }

        function exitExam() {
            alert('Saliendo del examen...');
            window.location.href = '06_student_dashboard.html';
        }

        // Initialize timers
        startPrepTimer();
        startExamTimer();
        
        // Prevent accidental page refresh
        window.addEventListener('beforeunload', function (e) {
            e.preventDefault();
            e.returnValue = '';
        });
    </script>
</body>
</html>
