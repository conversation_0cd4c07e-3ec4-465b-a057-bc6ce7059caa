<!DOCTYPE html>
<html lang="es" class="h-full bg-gray-50">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Creación de Curso - Arroyo University</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
    </style>
</head>
<body class="h-full">
    <div class="min-h-full">
        <!-- Navigation -->
        <nav class="bg-white shadow">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.84L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3z"/>
                            </svg>
                        </div>
                        <h1 class="ml-3 text-xl font-semibold text-gray-900">Universidad Ejemplo</h1>
                        <nav class="ml-8 flex space-x-4">
                            <a href="05_content_creator_dashboard.html" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">Dashboard</a>
                            <a href="#" class="bg-blue-100 text-blue-700 px-3 py-2 rounded-md text-sm font-medium">Cursos</a>
                            <a href="09_question_bank.html" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">Preguntas</a>
                            <a href="12_exam_creation_wizard.html" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">Exámenes</a>
                        </nav>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                            <span class="text-sm font-medium text-gray-700">MG</span>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="mb-8">
                <h2 class="text-2xl font-bold text-gray-900">Crear Nuevo Curso</h2>
                <p class="mt-1 text-gray-600">Configura un nuevo curso para tus estudiantes</p>
            </div>

            <!-- Course Creation Form -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-8">
                    <form class="space-y-8">
                        <!-- Basic Information -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Información Básica</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div class="md:col-span-2">
                                    <label for="course-name" class="block text-sm font-medium text-gray-700 mb-2">
                                        Nombre del Curso *
                                    </label>
                                    <input type="text" id="course-name" required
                                           placeholder="Introducción a la Programación"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                </div>

                                <div>
                                    <label for="course-code" class="block text-sm font-medium text-gray-700 mb-2">
                                        Código del Curso
                                    </label>
                                    <input type="text" id="course-code"
                                           placeholder="PROG-101"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                </div>

                                <div>
                                    <label for="course-category" class="block text-sm font-medium text-gray-700 mb-2">
                                        Categoría *
                                    </label>
                                    <select id="course-category" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="">Seleccionar categoría</option>
                                        <option>Tecnología</option>
                                        <option>Idiomas</option>
                                        <option>Matemáticas</option>
                                        <option>Ciencias</option>
                                        <option>Historia</option>
                                        <option>Arte y Diseño</option>
                                        <option>Negocios</option>
                                        <option>Medicina</option>
                                        <option>Otro</option>
                                    </select>
                                </div>
                                
                                <div class="md:col-span-2">
                                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                                        Descripción
                                    </label>
                                    <textarea id="description" rows="3"
                                              placeholder="Describe los objetivos del curso, metodología y qué aprenderán los estudiantes. Por ejemplo: conceptos fundamentales, habilidades prácticas, proyectos, etc."
                                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Course Structure -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Tipos de Evaluación</h3>
                            <div class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-900">Preguntas de opción múltiple</h4>
                                        <p class="text-sm text-gray-500">Preguntas con múltiples opciones de respuesta</p>
                                    </div>
                                    <button type="button" class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent bg-blue-600 transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                        <span class="translate-x-5 inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"></span>
                                    </button>
                                </div>

                                <div class="flex items-center justify-between">
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-900">Preguntas de desarrollo</h4>
                                        <p class="text-sm text-gray-500">Respuestas escritas extensas</p>
                                    </div>
                                    <button type="button" class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent bg-blue-600 transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                        <span class="translate-x-5 inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"></span>
                                    </button>
                                </div>

                                <div class="flex items-center justify-between">
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-900">Preguntas con multimedia</h4>
                                        <p class="text-sm text-gray-500">Incluye audio, video o imágenes</p>
                                    </div>
                                    <button type="button" class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent bg-blue-600 transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                        <span class="translate-x-5 inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"></span>
                                    </button>
                                </div>

                                <div class="flex items-center justify-between">
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-900">Preguntas prácticas</h4>
                                        <p class="text-sm text-gray-500">Ejercicios hands-on o simulaciones</p>
                                    </div>
                                    <button type="button" class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent bg-gray-200 transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                        <span class="translate-x-0 inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"></span>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Assessment Configuration -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Configuración de Evaluación</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="passing-score" class="block text-sm font-medium text-gray-700 mb-2">
                                        Puntuación mínima para aprobar (%)
                                    </label>
                                    <input type="number" id="passing-score" min="0" max="100" value="70"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                
                                <div>
                                    <label for="time-limit" class="block text-sm font-medium text-gray-700 mb-2">
                                        Tiempo límite por defecto (minutos)
                                    </label>
                                    <input type="number" id="time-limit" min="15" max="300" value="90"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                
                                <div>
                                    <label for="attempts" class="block text-sm font-medium text-gray-700 mb-2">
                                        Intentos permitidos
                                    </label>
                                    <select id="attempts" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="1" selected>1 intento</option>
                                        <option value="2">2 intentos</option>
                                        <option value="3">3 intentos</option>
                                        <option value="unlimited">Ilimitados</option>
                                    </select>
                                </div>
                                
                                <div>
                                    <label for="feedback-mode" class="block text-sm font-medium text-gray-700 mb-2">
                                        Modo de feedback
                                    </label>
                                    <select id="feedback-mode" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="immediate">Inmediato</option>
                                        <option value="after-submission" selected>Después del envío</option>
                                        <option value="manual">Solo manual</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Tags and Categories -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Organización</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="category" class="block text-sm font-medium text-gray-700 mb-2">
                                        Categoría
                                    </label>
                                    <select id="category" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="">Seleccionar categoría</option>
                                        <option>Tecnología</option>
                                        <option>Idiomas</option>
                                        <option>Matemáticas</option>
                                        <option>Ciencias</option>
                                        <option>Historia</option>
                                        <option>Arte y Diseño</option>
                                        <option>Negocios</option>
                                        <option>Medicina</option>
                                        <option>Otro</option>
                                    </select>
                                </div>
                                
                                <div>
                                    <label for="tags" class="block text-sm font-medium text-gray-700 mb-2">
                                        Etiquetas
                                    </label>
                                    <input type="text" id="tags"
                                           placeholder="programación, python, algoritmos"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                    <p class="mt-1 text-xs text-gray-500">Separar con comas</p>
                                </div>
                            </div>
                        </div>

                        <!-- AI Integration -->
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <h4 class="text-sm font-medium text-blue-900 mb-2">Integración con IA</h4>
                            <div class="space-y-3">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-sm text-blue-800">Generar preguntas automáticamente</p>
                                        <p class="text-xs text-blue-600">Crear 20 preguntas iniciales usando IA</p>
                                    </div>
                                    <button type="button" class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent bg-blue-600 transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                        <span class="translate-x-5 inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"></span>
                                    </button>
                                </div>
                                <div class="text-xs text-blue-600">
                                    Costo estimado: ~200 tokens IA
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex justify-between pt-6 border-t">
                            <button type="button" onclick="window.location.href='05_content_creator_dashboard.html'" 
                                    class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 font-medium">
                                Cancelar
                            </button>
                            <div class="flex space-x-3">
                                <button type="button" onclick="saveDraft()" 
                                        class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 font-medium">
                                    Guardar Borrador
                                </button>
                                <button type="button" onclick="createCourse()" 
                                        class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 font-medium">
                                    Crear Curso
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </main>
    </div>

    <script>
        function saveDraft() {
            alert('Borrador guardado exitosamente');
        }

        function createCourse() {
            const courseName = document.getElementById('course-name').value;
            const courseCode = document.getElementById('course-code').value;
            const category = document.getElementById('course-category').value;
            if (!courseName) {
                alert('Por favor, ingresa un nombre para el curso');
                return;
            }

            if (!category) {
                alert('Por favor, selecciona una categoría');
                return;
            }

            alert(`¡Curso "${courseName}" creado exitosamente!\n\nCódigo: ${courseCode || 'Auto-generado'}\nCategoría: ${category}\n\nAhora puedes comenzar a agregar preguntas y configurar exámenes.`);
            window.location.href = '09_question_bank.html';
        }
    </script>
</body>
</html>
