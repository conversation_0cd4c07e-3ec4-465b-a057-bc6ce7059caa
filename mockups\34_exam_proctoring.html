<!DOCTYPE html>
<html lang="es" class="h-full bg-gray-50">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Supervisión de Examen - Arroyo University</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .video-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }
    </style>
</head>
<body class="h-full">
    <div class="min-h-full">
        <!-- Header -->
        <header class="bg-white shadow">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-4">
                    <div class="flex items-center">
                        <button onclick="window.location.href='05_content_creator_dashboard.html'" 
                                class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center hover:bg-blue-700 transition-colors mr-3">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                            </svg>
                        </button>
                        <div>
                            <h1 class="text-lg font-semibold text-gray-900">Supervisión de Examen</h1>
                            <p class="text-sm text-gray-600">English B2 Assessment - Sesión en vivo</p>
                        </div>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center space-x-2 bg-red-50 px-3 py-1 rounded-full">
                            <div class="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                            <span class="text-red-700 text-sm font-medium">EN VIVO</span>
                        </div>
                        <div class="text-sm text-gray-600">
                            <span class="font-medium">23</span> estudiantes activos
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <!-- Control Panel -->
            <div class="bg-white shadow rounded-lg mb-6">
                <div class="px-6 py-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-6">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-blue-600" id="timer">01:23:45</div>
                                <div class="text-sm text-gray-500">Tiempo restante</div>
                            </div>
                            
                            <div class="text-center">
                                <div class="text-2xl font-bold text-green-600">23</div>
                                <div class="text-sm text-gray-500">Conectados</div>
                            </div>
                            
                            <div class="text-center">
                                <div class="text-2xl font-bold text-yellow-600">2</div>
                                <div class="text-sm text-gray-500">Alertas</div>
                            </div>
                        </div>
                        
                        <div class="flex items-center space-x-3">
                            <button onclick="sendMessage()" 
                                    class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.955 8.955 0 01-2.172-.268l-5.909 1.477a.75.75 0 01-.919-.919l1.477-5.909A8.955 8.955 0 013 12a8 8 0 018-8c4.418 0 8 3.582 8 8z"/>
                                </svg>
                                Mensaje Global
                            </button>
                            
                            <button onclick="endExam()" 
                                    class="inline-flex items-center px-3 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-red-600 hover:bg-red-700">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 10a1 1 0 011-1h4a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 01-1-1v-4z"/>
                                </svg>
                                Finalizar Examen
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Student Monitoring Grid -->
            <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
                <!-- Main Video Feed -->
                <div class="lg:col-span-3">
                    <div class="bg-white shadow rounded-lg">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-medium text-gray-900">Monitoreo de Estudiantes</h3>
                                <div class="flex items-center space-x-2">
                                    <button onclick="toggleView('grid')" 
                                            class="p-2 text-gray-400 hover:text-gray-600 bg-blue-50 rounded">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"/>
                                        </svg>
                                    </button>
                                    <button onclick="toggleView('list')" 
                                            class="p-2 text-gray-400 hover:text-gray-600">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="p-6">
                            <div class="video-grid">
                                <!-- Student 1 -->
                                <div class="relative bg-gray-900 rounded-lg overflow-hidden aspect-video">
                                    <div class="absolute inset-0 bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center">
                                        <div class="text-center text-white">
                                            <div class="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-2">
                                                <span class="text-lg font-bold">CL</span>
                                            </div>
                                            <div class="text-sm font-medium">Carlos López</div>
                                        </div>
                                    </div>
                                    <div class="absolute top-2 left-2">
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            Activo
                                        </span>
                                    </div>
                                    <div class="absolute top-2 right-2">
                                        <button onclick="flagStudent('carlos')" class="p-1 bg-red-500 text-white rounded-full hover:bg-red-600">
                                            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M3 6a3 3 0 013-3h10a1 1 0 01.8 1.6L14.25 8l2.55 3.4A1 1 0 0116 13H6a1 1 0 00-1 1v3a1 1 0 11-2 0V6z" clip-rule="evenodd"/>
                                            </svg>
                                        </button>
                                    </div>
                                    <div class="absolute bottom-2 left-2 text-white text-xs">
                                        Pregunta 15/25
                                    </div>
                                </div>

                                <!-- Student 2 -->
                                <div class="relative bg-gray-900 rounded-lg overflow-hidden aspect-video">
                                    <div class="absolute inset-0 bg-gradient-to-br from-purple-400 to-purple-600 flex items-center justify-center">
                                        <div class="text-center text-white">
                                            <div class="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-2">
                                                <span class="text-lg font-bold">AM</span>
                                            </div>
                                            <div class="text-sm font-medium">Ana Martínez</div>
                                        </div>
                                    </div>
                                    <div class="absolute top-2 left-2">
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                            Alerta
                                        </span>
                                    </div>
                                    <div class="absolute top-2 right-2">
                                        <button onclick="flagStudent('ana')" class="p-1 bg-red-500 text-white rounded-full hover:bg-red-600">
                                            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M3 6a3 3 0 013-3h10a1 1 0 01.8 1.6L14.25 8l2.55 3.4A1 1 0 0116 13H6a1 1 0 00-1 1v3a1 1 0 11-2 0V6z" clip-rule="evenodd"/>
                                            </svg>
                                        </button>
                                    </div>
                                    <div class="absolute bottom-2 left-2 text-white text-xs">
                                        Pregunta 12/25
                                    </div>
                                </div>

                                <!-- Student 3 -->
                                <div class="relative bg-gray-900 rounded-lg overflow-hidden aspect-video">
                                    <div class="absolute inset-0 bg-gradient-to-br from-green-400 to-green-600 flex items-center justify-center">
                                        <div class="text-center text-white">
                                            <div class="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-2">
                                                <span class="text-lg font-bold">JS</span>
                                            </div>
                                            <div class="text-sm font-medium">Juan Silva</div>
                                        </div>
                                    </div>
                                    <div class="absolute top-2 left-2">
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            Activo
                                        </span>
                                    </div>
                                    <div class="absolute top-2 right-2">
                                        <button onclick="flagStudent('juan')" class="p-1 bg-red-500 text-white rounded-full hover:bg-red-600">
                                            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M3 6a3 3 0 013-3h10a1 1 0 01.8 1.6L14.25 8l2.55 3.4A1 1 0 0116 13H6a1 1 0 00-1 1v3a1 1 0 11-2 0V6z" clip-rule="evenodd"/>
                                            </svg>
                                        </button>
                                    </div>
                                    <div class="absolute bottom-2 left-2 text-white text-xs">
                                        Pregunta 18/25
                                    </div>
                                </div>

                                <!-- Student 4 -->
                                <div class="relative bg-gray-900 rounded-lg overflow-hidden aspect-video">
                                    <div class="absolute inset-0 bg-gradient-to-br from-red-400 to-red-600 flex items-center justify-center">
                                        <div class="text-center text-white">
                                            <div class="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-2">
                                                <span class="text-lg font-bold">MR</span>
                                            </div>
                                            <div class="text-sm font-medium">María Rodríguez</div>
                                        </div>
                                    </div>
                                    <div class="absolute top-2 left-2">
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            Desconectado
                                        </span>
                                    </div>
                                    <div class="absolute top-2 right-2">
                                        <button onclick="flagStudent('maria')" class="p-1 bg-red-500 text-white rounded-full hover:bg-red-600">
                                            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M3 6a3 3 0 013-3h10a1 1 0 01.8 1.6L14.25 8l2.55 3.4A1 1 0 0116 13H6a1 1 0 00-1 1v3a1 1 0 11-2 0V6z" clip-rule="evenodd"/>
                                            </svg>
                                        </button>
                                    </div>
                                    <div class="absolute bottom-2 left-2 text-white text-xs">
                                        Pregunta 8/25
                                    </div>
                                </div>

                                <!-- More students indicator -->
                                <div class="relative bg-gray-200 rounded-lg overflow-hidden aspect-video flex items-center justify-center">
                                    <div class="text-center text-gray-600">
                                        <div class="text-2xl font-bold">+19</div>
                                        <div class="text-sm">más estudiantes</div>
                                        <button onclick="showAllStudents()" class="mt-2 text-blue-600 hover:text-blue-500 text-sm font-medium">
                                            Ver todos
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="space-y-6">
                    <!-- Alerts Panel -->
                    <div class="bg-white shadow rounded-lg">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900">Alertas Activas</h3>
                        </div>
                        <div class="p-6">
                            <div class="space-y-4">
                                <div class="flex items-start space-x-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                                    <div class="w-2 h-2 bg-yellow-400 rounded-full mt-2"></div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm font-medium text-yellow-900">Ana Martínez</p>
                                        <p class="text-xs text-yellow-700">Múltiples cambios de ventana detectados</p>
                                        <p class="text-xs text-yellow-600">Hace 2 minutos</p>
                                    </div>
                                    <button onclick="reviewAlert('ana')" class="text-yellow-600 hover:text-yellow-500 text-xs">
                                        Revisar
                                    </button>
                                </div>
                                
                                <div class="flex items-start space-x-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                                    <div class="w-2 h-2 bg-red-400 rounded-full mt-2"></div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm font-medium text-red-900">María Rodríguez</p>
                                        <p class="text-xs text-red-700">Conexión perdida</p>
                                        <p class="text-xs text-red-600">Hace 5 minutos</p>
                                    </div>
                                    <button onclick="reviewAlert('maria')" class="text-red-600 hover:text-red-500 text-xs">
                                        Contactar
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="bg-white shadow rounded-lg">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900">Acciones Rápidas</h3>
                        </div>
                        <div class="p-6">
                            <div class="space-y-3">
                                <button onclick="extendTime()" 
                                        class="w-full flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                    Extender Tiempo
                                </button>
                                
                                <button onclick="pauseExam()" 
                                        class="w-full flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                    Pausar Examen
                                </button>
                                
                                <button onclick="exportReport()" 
                                        class="w-full flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                    </svg>
                                    Exportar Reporte
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Exam Progress -->
                    <div class="bg-white shadow rounded-lg">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900">Progreso General</h3>
                        </div>
                        <div class="p-6">
                            <div class="space-y-4">
                                <div>
                                    <div class="flex justify-between text-sm mb-1">
                                        <span class="text-gray-700">Completado</span>
                                        <span class="text-gray-900">68%</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-blue-600 h-2 rounded-full" style="width: 68%"></div>
                                    </div>
                                </div>
                                
                                <div class="grid grid-cols-2 gap-4 text-center">
                                    <div>
                                        <div class="text-lg font-bold text-green-600">21</div>
                                        <div class="text-xs text-gray-500">Finalizados</div>
                                    </div>
                                    <div>
                                        <div class="text-lg font-bold text-blue-600">2</div>
                                        <div class="text-xs text-gray-500">En progreso</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Timer countdown
        let timeLeft = 5025; // 1:23:45 in seconds
        
        function updateTimer() {
            const hours = Math.floor(timeLeft / 3600);
            const minutes = Math.floor((timeLeft % 3600) / 60);
            const seconds = timeLeft % 60;
            
            document.getElementById('timer').textContent = 
                `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            
            if (timeLeft > 0) {
                timeLeft--;
            }
        }
        
        setInterval(updateTimer, 1000);

        function flagStudent(studentId) {
            alert(`Marcando alerta para estudiante: ${studentId}`);
        }

        function sendMessage() {
            const message = prompt('Mensaje para todos los estudiantes:');
            if (message) {
                alert(`Mensaje enviado: "${message}"`);
            }
        }

        function endExam() {
            if (confirm('¿Estás seguro de que quieres finalizar el examen para todos los estudiantes?')) {
                alert('Examen finalizado. Redirigiendo a resultados...');
                window.location.href = '20_manual_scoring.html';
            }
        }

        function toggleView(view) {
            alert(`Cambiando a vista: ${view}`);
        }

        function showAllStudents() {
            alert('Mostrando todos los estudiantes en vista expandida');
        }

        function reviewAlert(studentId) {
            alert(`Revisando alerta de: ${studentId}`);
        }

        function extendTime() {
            const minutes = prompt('¿Cuántos minutos adicionales?');
            if (minutes) {
                alert(`Tiempo extendido en ${minutes} minutos`);
                timeLeft += parseInt(minutes) * 60;
            }
        }

        function pauseExam() {
            if (confirm('¿Pausar el examen para todos los estudiantes?')) {
                alert('Examen pausado');
            }
        }

        function exportReport() {
            alert('Generando reporte de supervisión...');
        }
    </script>
</body>
</html>
