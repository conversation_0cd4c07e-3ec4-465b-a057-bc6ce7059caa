<!DOCTYPE html>
<html lang="es" class="h-full bg-gray-50">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Marketplace de Cursos - Arroyo University</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
    </style>
</head>
<body class="h-full">
    <div class="min-h-full">
        <!-- Navigation -->
        <nav class="bg-white shadow">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <button onclick="window.location.href='06_student_dashboard.html'" 
                                class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center hover:bg-blue-700 transition-colors">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.84L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3z"/>
                            </svg>
                        </button>
                        <h1 class="ml-3 text-xl font-semibold text-gray-900">Universidad Ejemplo</h1>
                        <nav class="ml-8 flex space-x-4">
                            <a href="06_student_dashboard.html" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">Dashboard</a>
                            <a href="#" class="bg-blue-100 text-blue-700 px-3 py-2 rounded-md text-sm font-medium">Marketplace</a>
                            <a href="#" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">Mis Cursos</a>
                            <a href="#" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">Exámenes</a>
                        </nav>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                            <span class="text-sm font-medium text-gray-700">CL</span>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="mb-8">
                <div class="flex items-center justify-between">
                    <div>
                        <h2 class="text-2xl font-bold text-gray-900">Marketplace de Cursos</h2>
                        <p class="mt-1 text-gray-600">Descubre y únete a cursos disponibles en tu organización</p>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <div class="relative">
                            <input type="text" placeholder="Buscar cursos..."
                                   class="w-64 px-4 py-2 pl-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <svg class="absolute left-3 top-2.5 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                            </svg>
                        </div>
                        <button class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"/>
                            </svg>
                            Filtros
                        </button>
                    </div>
                </div>
            </div>

            <!-- Categories -->
            <div class="mb-8">
                <div class="flex flex-wrap gap-2">
                    <button class="px-4 py-2 bg-blue-100 text-blue-700 rounded-full text-sm font-medium">Todos</button>
                    <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-full text-sm font-medium hover:bg-gray-200">Tecnología</button>
                    <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-full text-sm font-medium hover:bg-gray-200">Idiomas</button>
                    <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-full text-sm font-medium hover:bg-gray-200">Matemáticas</button>
                    <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-full text-sm font-medium hover:bg-gray-200">Ciencias</button>
                    <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-full text-sm font-medium hover:bg-gray-200">Negocios</button>
                    <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-full text-sm font-medium hover:bg-gray-200">Arte</button>
                </div>
            </div>

            <!-- Featured Courses -->
            <div class="mb-8">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Cursos Destacados</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- DevOps Course -->
                    <div class="bg-white shadow rounded-lg overflow-hidden hover:shadow-lg transition-shadow">
                        <div class="h-48 bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                            <div class="text-center text-white">
                                <svg class="w-16 h-16 mx-auto mb-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                </svg>
                                <h4 class="text-xl font-bold">DevOps Fundamentals</h4>
                            </div>
                        </div>
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-3">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    Tecnología
                                </span>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    Intermedio
                                </span>
                            </div>
                            <h4 class="text-lg font-medium text-gray-900 mb-2">DevOps Fundamentals</h4>
                            <p class="text-gray-600 text-sm mb-4">
                                Aprende los principios fundamentales de DevOps, CI/CD, containerización y automatización.
                            </p>
                            <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                                <span>👨‍🏫 Prof. García</span>
                                <span>⏱️ 8 semanas</span>
                                <span>👥 156 estudiantes</span>
                            </div>
                            <button onclick="enrollCourse('devops')" 
                                    class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 font-medium">
                                Inscribirse
                            </button>
                        </div>
                    </div>

                    <!-- AI Basics Course -->
                    <div class="bg-white shadow rounded-lg overflow-hidden hover:shadow-lg transition-shadow">
                        <div class="h-48 bg-gradient-to-br from-purple-500 to-pink-600 flex items-center justify-center">
                            <div class="text-center text-white">
                                <svg class="w-16 h-16 mx-auto mb-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                <h4 class="text-xl font-bold">IA Básica</h4>
                            </div>
                        </div>
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-3">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                    Tecnología
                                </span>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    Principiante
                                </span>
                            </div>
                            <h4 class="text-lg font-medium text-gray-900 mb-2">Introducción a la Inteligencia Artificial</h4>
                            <p class="text-gray-600 text-sm mb-4">
                                Conceptos básicos de IA, machine learning y aplicaciones prácticas en el mundo real.
                            </p>
                            <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                                <span>👩‍🏫 Dra. Martínez</span>
                                <span>⏱️ 6 semanas</span>
                                <span>👥 243 estudiantes</span>
                            </div>
                            <button onclick="enrollCourse('ai-basics')" 
                                    class="w-full px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 font-medium">
                                Inscribirse
                            </button>
                        </div>
                    </div>

                    <!-- Prompt Engineering Course -->
                    <div class="bg-white shadow rounded-lg overflow-hidden hover:shadow-lg transition-shadow">
                        <div class="h-48 bg-gradient-to-br from-green-500 to-teal-600 flex items-center justify-center">
                            <div class="text-center text-white">
                                <svg class="w-16 h-16 mx-auto mb-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                                </svg>
                                <h4 class="text-xl font-bold">Prompt Engineering</h4>
                            </div>
                        </div>
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-3">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    Tecnología
                                </span>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                    Avanzado
                                </span>
                            </div>
                            <h4 class="text-lg font-medium text-gray-900 mb-2">Prompt Engineering Avanzado</h4>
                            <p class="text-gray-600 text-sm mb-4">
                                Domina el arte de crear prompts efectivos para modelos de IA y maximiza su potencial.
                            </p>
                            <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                                <span>👨‍💻 Ing. López</span>
                                <span>⏱️ 4 semanas</span>
                                <span>👥 89 estudiantes</span>
                            </div>
                            <button onclick="enrollCourse('prompt-engineering')" 
                                    class="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 font-medium">
                                Inscribirse
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- All Courses Grid -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Todos los Cursos</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                        <!-- Python Programming -->
                        <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                            <div class="flex items-center justify-between mb-2">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    Tecnología
                                </span>
                                <span class="text-xs text-gray-500">⭐ 4.8</span>
                            </div>
                            <h4 class="text-sm font-medium text-gray-900 mb-1">Python para Principiantes</h4>
                            <p class="text-xs text-gray-600 mb-3">Fundamentos de programación en Python</p>
                            <div class="flex items-center justify-between text-xs text-gray-500 mb-3">
                                <span>👨‍🏫 Prof. Silva</span>
                                <span>👥 312</span>
                            </div>
                            <button onclick="enrollCourse('python')" 
                                    class="w-full px-3 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700">
                                Inscribirse
                            </button>
                        </div>

                        <!-- Data Science -->
                        <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                            <div class="flex items-center justify-between mb-2">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                    Tecnología
                                </span>
                                <span class="text-xs text-gray-500">⭐ 4.6</span>
                            </div>
                            <h4 class="text-sm font-medium text-gray-900 mb-1">Ciencia de Datos</h4>
                            <p class="text-xs text-gray-600 mb-3">Análisis de datos con Python y R</p>
                            <div class="flex items-center justify-between text-xs text-gray-500 mb-3">
                                <span>👩‍🏫 Dra. Chen</span>
                                <span>👥 187</span>
                            </div>
                            <button onclick="enrollCourse('data-science')" 
                                    class="w-full px-3 py-1 bg-purple-600 text-white text-xs rounded hover:bg-purple-700">
                                Inscribirse
                            </button>
                        </div>

                        <!-- English B2 -->
                        <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                            <div class="flex items-center justify-between mb-2">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    Idiomas
                                </span>
                                <span class="text-xs text-gray-500">⭐ 4.9</span>
                            </div>
                            <h4 class="text-sm font-medium text-gray-900 mb-1">English B2 Intermediate</h4>
                            <p class="text-xs text-gray-600 mb-3">Inglés intermedio alto</p>
                            <div class="flex items-center justify-between text-xs text-gray-500 mb-3">
                                <span>👩‍🏫 Prof. Johnson</span>
                                <span>👥 456</span>
                            </div>
                            <button onclick="enrollCourse('english-b2')" 
                                    class="w-full px-3 py-1 bg-green-600 text-white text-xs rounded hover:bg-green-700">
                                Inscribirse
                            </button>
                        </div>

                        <!-- Digital Marketing -->
                        <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                            <div class="flex items-center justify-between mb-2">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    Negocios
                                </span>
                                <span class="text-xs text-gray-500">⭐ 4.7</span>
                            </div>
                            <h4 class="text-sm font-medium text-gray-900 mb-1">Marketing Digital</h4>
                            <p class="text-xs text-gray-600 mb-3">Estrategias de marketing online</p>
                            <div class="flex items-center justify-between text-xs text-gray-500 mb-3">
                                <span>👨‍💼 Lic. Ruiz</span>
                                <span>👥 234</span>
                            </div>
                            <button onclick="enrollCourse('marketing')" 
                                    class="w-full px-3 py-1 bg-yellow-600 text-white text-xs rounded hover:bg-yellow-700">
                                Inscribirse
                            </button>
                        </div>

                        <!-- Calculus -->
                        <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                            <div class="flex items-center justify-between mb-2">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    Matemáticas
                                </span>
                                <span class="text-xs text-gray-500">⭐ 4.5</span>
                            </div>
                            <h4 class="text-sm font-medium text-gray-900 mb-1">Cálculo Diferencial</h4>
                            <p class="text-xs text-gray-600 mb-3">Fundamentos del cálculo</p>
                            <div class="flex items-center justify-between text-xs text-gray-500 mb-3">
                                <span>👨‍🏫 Dr. Pérez</span>
                                <span>👥 198</span>
                            </div>
                            <button onclick="enrollCourse('calculus')" 
                                    class="w-full px-3 py-1 bg-red-600 text-white text-xs rounded hover:bg-red-700">
                                Inscribirse
                            </button>
                        </div>

                        <!-- Photography -->
                        <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                            <div class="flex items-center justify-between mb-2">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-pink-100 text-pink-800">
                                    Arte
                                </span>
                                <span class="text-xs text-gray-500">⭐ 4.8</span>
                            </div>
                            <h4 class="text-sm font-medium text-gray-900 mb-1">Fotografía Digital</h4>
                            <p class="text-xs text-gray-600 mb-3">Técnicas de fotografía moderna</p>
                            <div class="flex items-center justify-between text-xs text-gray-500 mb-3">
                                <span>📸 Prof. Vega</span>
                                <span>👥 145</span>
                            </div>
                            <button onclick="enrollCourse('photography')" 
                                    class="w-full px-3 py-1 bg-pink-600 text-white text-xs rounded hover:bg-pink-700">
                                Inscribirse
                            </button>
                        </div>

                        <!-- Biology -->
                        <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                            <div class="flex items-center justify-between mb-2">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-teal-100 text-teal-800">
                                    Ciencias
                                </span>
                                <span class="text-xs text-gray-500">⭐ 4.6</span>
                            </div>
                            <h4 class="text-sm font-medium text-gray-900 mb-1">Biología Molecular</h4>
                            <p class="text-xs text-gray-600 mb-3">Fundamentos de biología celular</p>
                            <div class="flex items-center justify-between text-xs text-gray-500 mb-3">
                                <span>🔬 Dra. Torres</span>
                                <span>👥 167</span>
                            </div>
                            <button onclick="enrollCourse('biology')" 
                                    class="w-full px-3 py-1 bg-teal-600 text-white text-xs rounded hover:bg-teal-700">
                                Inscribirse
                            </button>
                        </div>

                        <!-- History -->
                        <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                            <div class="flex items-center justify-between mb-2">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                                    Historia
                                </span>
                                <span class="text-xs text-gray-500">⭐ 4.7</span>
                            </div>
                            <h4 class="text-sm font-medium text-gray-900 mb-1">Historia Contemporánea</h4>
                            <p class="text-xs text-gray-600 mb-3">Siglos XX y XXI</p>
                            <div class="flex items-center justify-between text-xs text-gray-500 mb-3">
                                <span>📚 Prof. Morales</span>
                                <span>👥 223</span>
                            </div>
                            <button onclick="enrollCourse('history')" 
                                    class="w-full px-3 py-1 bg-indigo-600 text-white text-xs rounded hover:bg-indigo-700">
                                Inscribirse
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        function enrollCourse(courseId) {
            alert(`¡Te has inscrito exitosamente al curso: ${courseId}!\n\nRecibirás un email de confirmación con los detalles del curso y las fechas de inicio.`);
        }
    </script>
</body>
</html>
