<!DOCTYPE html>
<html lang="es" class="h-full bg-gray-50">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pregunta Listening - Arroyo University</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .audio-progress {
            background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
            transition: width 0.1s ease;
        }
    </style>
</head>
<body class="h-full">
    <div class="min-h-full">
        <!-- Header with Timer -->
        <header class="bg-white shadow">
            <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-4">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.84L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3z"/>
                            </svg>
                        </div>
                        <h1 class="ml-3 text-lg font-semibold text-gray-900">English B2 Assessment</h1>
                    </div>
                    
                    <div class="flex items-center space-x-6">
                        <!-- Progress -->
                        <div class="text-sm text-gray-600">
                            Pregunta <span class="font-medium">12 de 25</span>
                        </div>
                        
                        <!-- Timer -->
                        <div class="flex items-center space-x-2 bg-red-50 px-3 py-1 rounded-full">
                            <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                            <span class="text-red-600 font-medium text-sm">52:34</span>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Progress Bar -->
        <div class="bg-white border-b">
            <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="py-2">
                    <div class="w-full bg-gray-200 rounded-full h-1">
                        <div class="bg-blue-600 h-1 rounded-full" style="width: 48%"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <main class="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
            <!-- Question Header -->
            <div class="mb-6">
                <div class="flex items-center space-x-3 mb-4">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z"/>
                        </svg>
                        Listening
                    </span>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                        B2 Level
                    </span>
                </div>
                <h2 class="text-xl font-semibold text-gray-900">Pregunta 12</h2>
            </div>

            <!-- Question Content -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-8">
                    <!-- Audio Player Section -->
                    <div class="mb-8">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">
                            Escucha la conversación y responde las preguntas
                        </h3>
                        
                        <!-- Audio Player -->
                        <div class="bg-gray-50 border border-gray-200 rounded-lg p-6">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center space-x-3">
                                    <button id="playBtn" onclick="togglePlay()" 
                                            class="w-12 h-12 bg-blue-600 hover:bg-blue-700 rounded-full flex items-center justify-center transition-colors">
                                        <svg id="playIcon" class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"/>
                                        </svg>
                                    </button>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">Conversación: En el restaurante</p>
                                        <p class="text-xs text-gray-500">Duración: 2:15</p>
                                    </div>
                                </div>
                                
                                <div class="flex items-center space-x-2">
                                    <span class="text-sm text-gray-600">Reproducciones:</span>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        1 de 2
                                    </span>
                                </div>
                            </div>
                            
                            <!-- Progress Bar -->
                            <div class="mb-4">
                                <div class="flex items-center justify-between text-xs text-gray-500 mb-1">
                                    <span id="currentTime">0:00</span>
                                    <span>2:15</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div id="audioProgress" class="audio-progress h-2 rounded-full" style="width: 0%"></div>
                                </div>
                            </div>
                            
                            <!-- Audio Controls -->
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <button onclick="changeSpeed()" class="text-sm text-gray-600 hover:text-gray-800 px-2 py-1 rounded border border-gray-300">
                                        <span id="speedText">1x</span>
                                    </button>
                                    <button onclick="rewind()" class="text-gray-600 hover:text-gray-800">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12.066 11.2a1 1 0 000 1.6l5.334 4A1 1 0 0019 16V8a1 1 0 00-1.6-.8l-5.333 4zM4.066 11.2a1 1 0 000 1.6l5.334 4A1 1 0 0011 16V8a1 1 0 00-1.6-.8l-5.334 4z"/>
                                        </svg>
                                    </button>
                                    <button onclick="forward()" class="text-gray-600 hover:text-gray-800">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.933 12.8a1 1 0 000-1.6L6.6 7.2A1 1 0 005 8v8a1 1 0 001.6.8l5.333-4zM19.933 12.8a1 1 0 000-1.6l-5.333-4A1 1 0 0013 8v8a1 1 0 001.6.8l5.333-4z"/>
                                        </svg>
                                    </button>
                                </div>
                                
                                <div class="flex items-center space-x-2">
                                    <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728"/>
                                    </svg>
                                    <input type="range" min="0" max="100" value="75" class="w-20 h-1 bg-gray-200 rounded-lg appearance-none cursor-pointer">
                                </div>
                            </div>
                        </div>
                        
                        <!-- Transcript Toggle -->
                        <div class="mt-4">
                            <button onclick="toggleTranscript()" class="text-sm text-blue-600 hover:text-blue-500 font-medium">
                                <span id="transcriptToggle">Mostrar transcripción</span>
                                <svg class="w-4 h-4 inline ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                                </svg>
                            </button>
                            
                            <div id="transcript" class="hidden mt-3 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                                <p class="text-sm text-blue-900 italic">
                                    <strong>Camarero:</strong> Buenas tardes, ¿mesa para cuántas personas?<br>
                                    <strong>Cliente:</strong> Para dos, por favor. ¿Tienen alguna mesa cerca de la ventana?<br>
                                    <strong>Camarero:</strong> Sí, síganme por favor. Aquí tienen la carta. ¿Les gustaría algo de beber para empezar?<br>
                                    <strong>Cliente:</strong> Sí, dos aguas con gas y podrían traernos también la carta de vinos...
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Questions -->
                    <div class="space-y-6">
                        <!-- Question 1 -->
                        <div class="border-t pt-6">
                            <h4 class="text-base font-medium text-gray-900 mb-4">
                                1. ¿Para cuántas personas solicita mesa el cliente?
                            </h4>
                            <div class="space-y-3">
                                <label class="flex items-center">
                                    <input type="radio" name="q1" value="a" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                                    <span class="ml-3 text-gray-700">Una persona</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="q1" value="b" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300" checked>
                                    <span class="ml-3 text-gray-700">Dos personas</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="q1" value="c" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                                    <span class="ml-3 text-gray-700">Tres personas</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="q1" value="d" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                                    <span class="ml-3 text-gray-700">Cuatro personas</span>
                                </label>
                            </div>
                        </div>

                        <!-- Question 2 -->
                        <div class="border-t pt-6">
                            <h4 class="text-base font-medium text-gray-900 mb-4">
                                2. ¿Qué tipo de mesa prefiere el cliente?
                            </h4>
                            <div class="space-y-3">
                                <label class="flex items-center">
                                    <input type="radio" name="q2" value="a" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                                    <span class="ml-3 text-gray-700">Una mesa en el centro del restaurante</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="q2" value="b" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                                    <span class="ml-3 text-gray-700">Una mesa cerca de la ventana</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="q2" value="c" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                                    <span class="ml-3 text-gray-700">Una mesa en la terraza</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="q2" value="d" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                                    <span class="ml-3 text-gray-700">Una mesa privada</span>
                                </label>
                            </div>
                        </div>

                        <!-- Question 3 -->
                        <div class="border-t pt-6">
                            <h4 class="text-base font-medium text-gray-900 mb-4">
                                3. ¿Qué bebidas solicita el cliente inicialmente?
                            </h4>
                            <div class="space-y-3">
                                <label class="flex items-center">
                                    <input type="radio" name="q3" value="a" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                                    <span class="ml-3 text-gray-700">Dos refrescos</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="q3" value="b" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                                    <span class="ml-3 text-gray-700">Dos aguas naturales</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="q3" value="c" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                                    <span class="ml-3 text-gray-700">Dos aguas con gas</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="q3" value="d" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                                    <span class="ml-3 text-gray-700">Dos cervezas</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Auto-save Indicator -->
            <div class="mt-4 flex items-center justify-center">
                <div class="flex items-center text-sm text-gray-500">
                    <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    </svg>
                    <span>Respuestas guardadas automáticamente</span>
                </div>
            </div>

            <!-- Navigation -->
            <div class="mt-8 flex justify-between">
                <button class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                    </svg>
                    Anterior
                </button>
                
                <button class="inline-flex items-center px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Siguiente
                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                </button>
            </div>
        </main>
    </div>

    <script>
        let isPlaying = false;
        let currentTime = 0;
        let duration = 135; // 2:15 in seconds
        let playCount = 1;
        let speed = 1;
        let interval;

        function togglePlay() {
            const playBtn = document.getElementById('playBtn');
            const playIcon = document.getElementById('playIcon');
            
            if (!isPlaying) {
                // Start playing
                isPlaying = true;
                playIcon.innerHTML = '<path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 002 0V8a1 1 0 00-1-1z" clip-rule="evenodd"/>';
                
                interval = setInterval(() => {
                    currentTime += 1;
                    updateProgress();
                    if (currentTime >= duration) {
                        stopPlaying();
                    }
                }, 1000);
            } else {
                // Pause
                isPlaying = false;
                playIcon.innerHTML = '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"/>';
                clearInterval(interval);
            }
        }

        function stopPlaying() {
            isPlaying = false;
            clearInterval(interval);
            const playIcon = document.getElementById('playIcon');
            playIcon.innerHTML = '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"/>';
        }

        function updateProgress() {
            const progress = (currentTime / duration) * 100;
            document.getElementById('audioProgress').style.width = progress + '%';
            
            const minutes = Math.floor(currentTime / 60);
            const seconds = currentTime % 60;
            document.getElementById('currentTime').textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
        }

        function rewind() {
            currentTime = Math.max(0, currentTime - 10);
            updateProgress();
        }

        function forward() {
            currentTime = Math.min(duration, currentTime + 10);
            updateProgress();
        }

        function changeSpeed() {
            const speeds = [1, 1.25, 1.5, 0.75];
            const currentIndex = speeds.indexOf(speed);
            speed = speeds[(currentIndex + 1) % speeds.length];
            document.getElementById('speedText').textContent = speed + 'x';
        }

        function toggleTranscript() {
            const transcript = document.getElementById('transcript');
            const toggle = document.getElementById('transcriptToggle');
            
            if (transcript.classList.contains('hidden')) {
                transcript.classList.remove('hidden');
                toggle.textContent = 'Ocultar transcripción';
            } else {
                transcript.classList.add('hidden');
                toggle.textContent = 'Mostrar transcripción';
            }
        }
    </script>
</body>
</html>
