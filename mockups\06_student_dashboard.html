<!DOCTYPE html>
<html lang="es" class="h-full bg-gray-50">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Estudiante - Arroyo University</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
    </style>
</head>
<body class="h-full">
    <div class="min-h-full">
        <!-- Navigation -->
        <nav class="bg-white shadow">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.84L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3z"/>
                            </svg>
                        </div>
                        <h1 class="ml-3 text-xl font-semibold text-gray-900">Universidad Ejemplo</h1>
                        <nav class="ml-8 flex space-x-4">
                            <a href="#" class="bg-blue-100 text-blue-700 px-3 py-2 rounded-md text-sm font-medium">Dashboard</a>
                            <a href="39_course_marketplace.html" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">Marketplace</a>
                            <a href="#" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">Mis Cursos</a>
                            <a href="#" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">Exámenes</a>
                            <a href="#" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">Resultados</a>
                        </nav>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <button class="relative p-2 text-gray-400 hover:text-gray-500">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h10a2 2 0 002-2V7a2 2 0 00-2-2H4a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                            </svg>
                            <span class="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-400"></span>
                        </button>
                        <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                            <span class="text-sm font-medium text-gray-700">CL</span>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <!-- Welcome Section -->
            <div class="mb-8">
                <h2 class="text-2xl font-bold text-gray-900">Bienvenido, Alejandro</h2>
                <p class="mt-1 text-gray-600">Continúa tu progreso de aprendizaje</p>
            </div>

            <!-- Progress Overview -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                                </svg>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Cursos Activos</dt>
                                    <dd class="text-lg font-medium text-gray-900">3</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 px-5 py-3">
                        <div class="text-sm">
                            <span class="text-blue-600 font-medium">2</span>
                            <span class="text-gray-500">en progreso</span>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Exámenes Completados</dt>
                                    <dd class="text-lg font-medium text-gray-900">7</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 px-5 py-3">
                        <div class="text-sm">
                            <span class="text-green-600 font-medium">B2.1</span>
                            <span class="text-gray-500">nivel actual</span>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-6 w-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
                                </svg>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Progreso General</dt>
                                    <dd class="text-lg font-medium text-gray-900">78%</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 px-5 py-3">
                        <div class="text-sm">
                            <span class="text-purple-600 font-medium">+12%</span>
                            <span class="text-gray-500">este mes</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Available Exams -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Exámenes Disponibles</h3>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-900">English B2 Assessment</h4>
                                        <p class="text-xs text-gray-500 mt-1">Evaluación intermedio alto • 90 min</p>
                                        <div class="flex items-center mt-2">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                Disponible
                                            </span>
                                            <span class="ml-2 text-xs text-gray-500">Vence: 25 Nov</span>
                                        </div>
                                    </div>
                                    <button onclick="window.location.href='15_exam_landing.html'" 
                                            class="px-4 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700">
                                        Iniciar
                                    </button>
                                </div>
                            </div>
                            
                            <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-900">Grammar Practice Test</h4>
                                        <p class="text-xs text-gray-500 mt-1">Práctica de gramática • 45 min</p>
                                        <div class="flex items-center mt-2">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                Práctica
                                            </span>
                                            <span class="ml-2 text-xs text-gray-500">Sin límite de tiempo</span>
                                        </div>
                                    </div>
                                    <button class="px-4 py-2 bg-gray-600 text-white text-sm rounded-md hover:bg-gray-700">
                                        Practicar
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Results -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Resultados Recientes</h3>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <div class="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
                                <div>
                                    <h4 class="text-sm font-medium text-green-900">Listening Comprehension</h4>
                                    <p class="text-xs text-green-700 mt-1">Completado el 10 Nov</p>
                                </div>
                                <div class="text-right">
                                    <div class="text-lg font-bold text-green-600">B2.2</div>
                                    <div class="text-xs text-green-600">85/100</div>
                                </div>
                            </div>
                            
                            <div class="flex items-center justify-between p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                                <div>
                                    <h4 class="text-sm font-medium text-yellow-900">Speaking Assessment</h4>
                                    <p class="text-xs text-yellow-700 mt-1">En revisión</p>
                                </div>
                                <div class="text-right">
                                    <div class="text-sm font-medium text-yellow-600">Pendiente</div>
                                    <div class="text-xs text-yellow-600">Resultado en 24h</div>
                                </div>
                            </div>
                            
                            <div class="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-lg">
                                <div>
                                    <h4 class="text-sm font-medium text-blue-900">Writing Practice</h4>
                                    <p class="text-xs text-blue-700 mt-1">Completado el 8 Nov</p>
                                </div>
                                <div class="text-right">
                                    <div class="text-lg font-bold text-blue-600">B1.3</div>
                                    <div class="text-xs text-blue-600">78/100</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-6">
                            <button onclick="window.location.href='21_results_dashboard.html'" 
                                    class="w-full text-center text-sm font-medium text-blue-600 hover:text-blue-500">
                                Ver todos los resultados →
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Learning Progress -->
            <div class="bg-white shadow rounded-lg mb-8">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Progreso de Aprendizaje</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-6">
                        <!-- Writing Progress -->
                        <div>
                            <div class="flex items-center justify-between mb-2">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"/>
                                    </svg>
                                    <span class="text-sm font-medium text-gray-900">Writing</span>
                                </div>
                                <span class="text-sm text-gray-600">B2.1 (82%)</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-blue-600 h-2 rounded-full" style="width: 82%"></div>
                            </div>
                        </div>

                        <!-- Listening Progress -->
                        <div>
                            <div class="flex items-center justify-between mb-2">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z"/>
                                    </svg>
                                    <span class="text-sm font-medium text-gray-900">Listening</span>
                                </div>
                                <span class="text-sm text-gray-600">B2.2 (85%)</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-green-600 h-2 rounded-full" style="width: 85%"></div>
                            </div>
                        </div>

                        <!-- Speaking Progress -->
                        <div>
                            <div class="flex items-center justify-between mb-2">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-purple-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"/>
                                    </svg>
                                    <span class="text-sm font-medium text-gray-900">Speaking</span>
                                </div>
                                <span class="text-sm text-gray-600">B1.3 (75%)</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-purple-600 h-2 rounded-full" style="width: 75%"></div>
                            </div>
                        </div>

                        <!-- Reading Progress -->
                        <div>
                            <div class="flex items-center justify-between mb-2">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                                    </svg>
                                    <span class="text-sm font-medium text-gray-900">Reading</span>
                                </div>
                                <span class="text-sm text-gray-600">B2.0 (80%)</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-yellow-600 h-2 rounded-full" style="width: 80%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Course Marketplace Preview -->
            <div class="bg-white shadow rounded-lg mb-8">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-900">Cursos Disponibles</h3>
                        <button onclick="window.location.href='39_course_marketplace.html'"
                                class="text-sm font-medium text-blue-600 hover:text-blue-500">
                            Ver todos →
                        </button>
                    </div>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <!-- DevOps Course -->
                        <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                            <div class="flex items-center justify-between mb-2">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    Tecnología
                                </span>
                                <span class="text-xs text-gray-500">⭐ 4.8</span>
                            </div>
                            <h4 class="text-sm font-medium text-gray-900 mb-1">DevOps Fundamentals</h4>
                            <p class="text-xs text-gray-600 mb-3">CI/CD, containerización y automatización</p>
                            <div class="flex items-center justify-between text-xs text-gray-500 mb-3">
                                <span>👨‍🏫 Prof. García</span>
                                <span>👥 156</span>
                            </div>
                            <button onclick="window.location.href='39_course_marketplace.html'"
                                    class="w-full px-3 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700">
                                Ver Curso
                            </button>
                        </div>

                        <!-- AI Course -->
                        <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                            <div class="flex items-center justify-between mb-2">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                    Tecnología
                                </span>
                                <span class="text-xs text-gray-500">⭐ 4.6</span>
                            </div>
                            <h4 class="text-sm font-medium text-gray-900 mb-1">Introducción a la IA</h4>
                            <p class="text-xs text-gray-600 mb-3">Machine learning y aplicaciones prácticas</p>
                            <div class="flex items-center justify-between text-xs text-gray-500 mb-3">
                                <span>👩‍🏫 Dra. Martínez</span>
                                <span>👥 243</span>
                            </div>
                            <button onclick="window.location.href='39_course_marketplace.html'"
                                    class="w-full px-3 py-1 bg-purple-600 text-white text-xs rounded hover:bg-purple-700">
                                Ver Curso
                            </button>
                        </div>

                        <!-- Python Course -->
                        <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                            <div class="flex items-center justify-between mb-2">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    Tecnología
                                </span>
                                <span class="text-xs text-gray-500">⭐ 4.9</span>
                            </div>
                            <h4 class="text-sm font-medium text-gray-900 mb-1">Python para Principiantes</h4>
                            <p class="text-xs text-gray-600 mb-3">Fundamentos de programación en Python</p>
                            <div class="flex items-center justify-between text-xs text-gray-500 mb-3">
                                <span>👨‍🏫 Prof. Silva</span>
                                <span>👥 312</span>
                            </div>
                            <button onclick="window.location.href='39_course_marketplace.html'"
                                    class="w-full px-3 py-1 bg-green-600 text-white text-xs rounded hover:bg-green-700">
                                Ver Curso
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recommendations -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h3 class="text-lg font-medium text-blue-900 mb-4">Recomendaciones Personalizadas</h3>
                <div class="space-y-3 text-blue-800">
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                        </svg>
                        <p>Explora cursos de <strong>Tecnología</strong> en el marketplace para ampliar tus habilidades</p>
                    </div>
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                        </svg>
                        <p>Toma el examen <strong>"English B2 Assessment"</strong> para obtener tu certificación</p>
                    </div>
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                        </svg>
                        <p>Revisa el feedback de tu último examen para mejorar tu rendimiento</p>
                    </div>
                </div>
            </div>
        </main>
    </div>
</body>
</html>
