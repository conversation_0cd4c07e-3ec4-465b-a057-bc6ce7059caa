# Alcance y Roadmap - Arroyo University

## Introducción

Este documento define el alcance detallado de la primera iteración (MVP) de Arroyo University, así como el roadmap estratégico para las siguientes fases de desarrollo. Se establecen claramente los límites funcionales, prioridades y cronograma de implementación.

---

## 1. <PERSON><PERSON><PERSON> de la Primera Iteración (MVP)

### 1.1 <PERSON><PERSON> de Alcance

| Dominio           | Incluido en v1                                                                                                                                                                                                        | Excluido de v1                                |
| ----------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | -------------------------------------------------- |
| **Cursos**        | CRUD de cursos y módulos; publicación, archivado y versionado inicial.                                                                                                                                            | Certificaciones externas, SCORM import/export.     |
| **English Test**  | • *Writing* → preguntas abiertas vía IA.<br>• *Listening* → guion + TTS + opciones múltiples.<br>• *Speaking* → pregunta abierta con grabación de audio y STT opcional.<br>• Escalabilidad para 10k candidatos concurrentes. | Traducción instantánea, proctoring avanzado.       |
| **Generación IA** | OpenAI LLM + Azure Speech (TTS & STT) + filtros moderación.                                                                                                                                                        | Modelos propietarios on‑prem.                      |
| **Roles**         | Multi‑tenant con RBAC editable (checklist estilo Discord).                                                                                                                                                        | ABAC completo, etiqueta de seguridad confidencial. |
| **Usuarios**      | Alta automática de "usuarios temporales" por invitación con caducidad configurable.                                                                                                                               | Gestión de freelances externos.                    |
| **Reportes**      | Resultados inmediatos al creador, exportación CSV y JSON.                                                                                                                                                         | Integración Power BI embebida.                     |

### 1.2 Funcionalidades Core MVP

#### Gestión de Contenido
- ✅ CRUD completo de cursos y módulos
- ✅ Carga y gestión de contenido multimedia (≤ 200MB)
- ✅ Versionado básico de contenido
- ✅ Estados: DRAFT, PUBLISHED, ARCHIVED
- ✅ Clonación de cursos con sufijo "(Copy)"

#### Sistema de Evaluación
- ✅ Banco de preguntas con tipos: Writing, Listening, Speaking
- ✅ Generación automática de preguntas vía IA
- ✅ Configuración de exámenes con temporizador
- ✅ Intentos de examen con autosave
- ✅ Scoring automático y manual override
- ✅ Detección básica de plagio

#### Multi-Tenancy y Seguridad
- ✅ Aislamiento completo por tenant
- ✅ RBAC editable con interfaz tipo Discord
- ✅ Autenticación JWT + MFA opcional
- ✅ Usuarios temporales con expiración
- ✅ Auditoría básica de acciones

#### Analítica y Reportes
- ✅ Dashboard básico con métricas clave
- ✅ Exportación CSV/JSON de resultados
- ✅ Tracking de uso de cuota IA
- ✅ Alertas básicas de sistema

---

## 2. Criterios de Aceptación MVP

### 2.1 Criterios Técnicos
- **Performance**: p95 < 200ms para operaciones CRUD
- **Escalabilidad**: Soporte para 10,000 usuarios concurrentes
- **Disponibilidad**: SLO 99% durante piloto
- **Seguridad**: Cumplimiento OWASP Top 10

### 2.2 Criterios Funcionales
- **Generación IA**: < 15s end-to-end para 5 preguntas
- **Scoring IA**: < 4s por respuesta individual
- **Usabilidad**: Onboarding completo < 30 minutos
- **Accesibilidad**: Lighthouse score ≥ 80

### 2.3 Criterios de Negocio
- **Piloto exitoso**: 2 tenants reales operativos
- **Satisfacción**: NPS ≥ 40 tras primer mes
- **Adopción**: 80% de usuarios completan primer examen
- **Costo**: < $0.50 USD por test completo (objetivo $0.30)

---

## 3. Roadmap Detallado

### 3.1 Fase 1: MVP Foundation (Meses 1-6)

#### Sprint 1-2: Infraestructura Base
- [ ] Configuración de entorno de desarrollo
- [ ] Arquitectura multi-tenant básica
- [ ] Base de datos PostgreSQL con RLS
- [ ] Autenticación JWT básica
- [ ] CI/CD pipeline inicial

#### Sprint 3-4: Gestión de Usuarios y Tenants
- [ ] CRUD de tenants y configuración
- [ ] Sistema de usuarios y roles básico
- [ ] Interfaz de administración tenant
- [ ] Invitaciones y usuarios temporales

#### Sprint 5-6: Banco de Preguntas
- [ ] Modelo de datos para preguntas
- [ ] CRUD manual de preguntas
- [ ] Integración básica con OpenAI
- [ ] Generación de preguntas Writing

#### Sprint 7-8: Servicios de IA
- [ ] Integración Azure Speech (TTS/STT)
- [ ] Generación de preguntas Listening
- [ ] Generación de preguntas Speaking
- [ ] Sistema de scoring básico

#### Sprint 9-10: Sistema de Exámenes
- [ ] CRUD de exámenes
- [ ] Motor de intentos de examen
- [ ] Interfaz de presentación
- [ ] Autosave y recuperación

#### Sprint 11-12: Scoring y Reportes
- [ ] Scoring automático MCQ
- [ ] Scoring IA para respuestas abiertas
- [ ] Dashboard básico
- [ ] Exportación de resultados

### 3.2 Fase 2: Expansión y Optimización (Meses 7-12)

#### Trimestre 3: Mejoras de UX y Performance
- [ ] PWA con capacidades offline
- [ ] Optimización de performance
- [ ] Mejoras de accesibilidad (WCAG 2.1)
- [ ] Internacionalización (ES/EN)

#### Trimestre 4: Integraciones y Analítica
- [ ] SSO empresarial (SAML/OIDC)
- [ ] Webhooks para integraciones
- [ ] Analítica avanzada y dashboards
- [ ] API pública v1

### 3.3 Fase 3: Marketplace y Extensibilidad (Meses 13-24)

#### Semestre 1: Plataforma Avanzada
- [ ] Sistema de plugins WebAssembly
- [ ] Marketplace de contenido
- [ ] Analítica predictiva básica
- [ ] Mobile apps nativas

#### Semestre 2: Expansión Global
- [ ] Soporte multiidioma completo
- [ ] Compliance regional (GDPR, CCPA)
- [ ] Integraciones LMS/HRIS avanzadas
- [ ] IA predictiva avanzada

---

## 4. Dependencias y Riesgos

### 4.1 Dependencias Críticas
- **OpenAI API**: Disponibilidad y límites de rate
- **Azure Speech**: Calidad TTS/STT en español
- **PostgreSQL**: Performance con multi-tenancy
- **React/FastAPI**: Estabilidad de frameworks

### 4.2 Riesgos del MVP
| Riesgo | Probabilidad | Impacto | Mitigación |
|--------|--------------|---------|------------|
| Límites API OpenAI | Media | Alto | Múltiples proveedores, caching |
| Performance multi-tenant | Media | Alto | Pruebas de carga tempranas |
| Complejidad RBAC | Alta | Medio | Prototipo temprano |
| Adopción lenta | Media | Alto | Piloto con clientes reales |

### 4.3 Supuestos Clave
- Disponibilidad de APIs de IA durante desarrollo
- Adopción de tecnologías React/FastAPI por el equipo
- Presupuesto suficiente para infraestructura cloud
- Acceso a usuarios beta para validación

---

## 5. Métricas de Seguimiento

### 5.1 Métricas de Desarrollo
- **Velocity**: Story points por sprint
- **Quality**: Bugs por feature, cobertura de tests
- **Performance**: Tiempo de respuesta, throughput
- **Security**: Vulnerabilidades detectadas/resueltas

### 5.2 Métricas de Producto
- **Adoption**: Usuarios activos, tenants onboarded
- **Engagement**: Exámenes completados, tiempo en plataforma
- **Satisfaction**: NPS, feedback scores
- **Technical**: Uptime, error rates, latencia

### 5.3 Métricas de Negocio
- **Cost**: Costo por test, costo por tenant
- **Revenue**: ARR, churn rate, expansion revenue
- **Market**: Market share, competitive position

---

## 6. Criterios de Go/No-Go

### 6.1 Para Fase 2
- ✅ MVP desplegado con 2+ tenants activos
- ✅ NPS ≥ 40 sostenido por 2 meses
- ✅ Performance targets alcanzados
- ✅ Feedback positivo de usuarios beta

### 6.2 Para Fase 3
- ✅ 10+ tenants activos con uso regular
- ✅ Revenue run-rate positivo
- ✅ Arquitectura escalable validada
- ✅ Equipo técnico consolidado

---

## Conclusión

El roadmap de Arroyo University está diseñado para entregar valor incremental mientras construye una base sólida para el crecimiento futuro. El enfoque en MVP permite validación temprana del mercado mientras que las fases posteriores aseguran escalabilidad y diferenciación competitiva.

La clave del éxito será la ejecución disciplinada del MVP con foco en calidad y experiencia de usuario, seguida de una expansión cuidadosa basada en feedback real de usuarios y métricas de adopción.
