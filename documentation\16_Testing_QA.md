# Testing y QA - Arroyo University

## Introducción

Este documento define la estrategia integral de testing y aseguramiento de calidad para Arroyo University. Incluye metodologías, herramientas, procesos y criterios de calidad para garantizar la entrega de software robusto, seguro y confiable.

---

## 1. Estrategia de Testing

### 1.1 Pirámide de Testing

```
                    ┌─────────────────┐
                    │   E2E Tests     │ 10%
                    │   (Cypress)     │
                ┌───┴─────────────────┴───┐
                │   Integration Tests     │ 20%
                │   (pytest, Postman)    │
            ┌───┴─────────────────────────┴───┐
            │        Unit Tests               │ 70%
            │   (pytest, jest, vitest)       │
            └─────────────────────────────────┘
```

### 1.2 Tipos de Testing por Componente

| Componente | Unit Tests | Integration | E2E | Performance | Security |
|------------|------------|-------------|-----|-------------|----------|
| **Frontend** | Jest/Vitest | React Testing Library | Cypress | Lighthouse | OWASP ZAP |
| **Backend API** | pytest | pytest + TestClient | Postman/Newman | k6 | Bandit |
| **Database** | pytest | pytest + DB | SQL Tests | pgbench | SQL injection |
| **AI Services** | pytest | Mock APIs | Real AI calls | Load testing | Input validation |

---

## 2. Testing Automatizado

### 2.1 Unit Testing

#### Frontend (React/TypeScript)
```typescript
// Example: Question component test
import { render, screen, fireEvent } from '@testing-library/react';
import { Question } from './Question';

describe('Question Component', () => {
  const mockQuestion = {
    id: '1',
    type: 'writing',
    prompt: 'Describe your ideal vacation',
    timeLimit: 900
  };

  test('should render question prompt correctly', () => {
    render(<Question question={mockQuestion} onAnswer={jest.fn()} />);

    expect(screen.getByText('Describe your ideal vacation')).toBeInTheDocument();
  });

  test('should call onAnswer when answer is submitted', () => {
    const mockOnAnswer = jest.fn();
    render(<Question question={mockQuestion} onAnswer={mockOnAnswer} />);

    const textarea = screen.getByRole('textbox');
    fireEvent.change(textarea, { target: { value: 'My ideal vacation...' } });

    const submitButton = screen.getByRole('button', { name: /submit/i });
    fireEvent.click(submitButton);

    expect(mockOnAnswer).toHaveBeenCalledWith({
      questionId: '1',
      answer: 'My ideal vacation...',
      timeSpent: expect.any(Number)
    });
  });

  test('should show time remaining countdown', () => {
    render(<Question question={mockQuestion} onAnswer={jest.fn()} />);

    expect(screen.getByText(/15:00/)).toBeInTheDocument(); // 15 minutes
  });
});
```

#### Backend (Python/FastAPI)
```python
# Example: User service tests
import pytest
from unittest.mock import Mock, patch
from app.services.user_service import UserService
from app.models.user import User

class TestUserService:
    @pytest.fixture
    def user_service(self):
        return UserService()

    @pytest.fixture
    def mock_user_data(self):
        return {
            "email": "<EMAIL>",
            "username": "testuser",
            "tenant_id": "tenant-123"
        }

    async def test_create_user_success(self, user_service, mock_user_data):
        # Given
        with patch('app.services.user_service.send_verification_email') as mock_email:
            # When
            user = await user_service.create_user(mock_user_data)

            # Then
            assert user.email == "<EMAIL>"
            assert user.verified is False
            mock_email.assert_called_once()

    async def test_create_user_duplicate_email(self, user_service, mock_user_data):
        # Given
        with patch('app.repositories.user_repo.get_by_email') as mock_get:
            mock_get.return_value = Mock()

            # When/Then
            with pytest.raises(UserAlreadyExistsException):
                await user_service.create_user(mock_user_data)

    async def test_verify_user_email(self, user_service):
        # Given
        token = "valid-verification-token"

        with patch('app.services.user_service.verify_token') as mock_verify:
            mock_verify.return_value = {"user_id": "user-123"}

            # When
            result = await user_service.verify_email(token)

            # Then
            assert result.verified is True
```

### 2.2 Integration Testing

#### API Integration Tests
```python
# Example: Exam flow integration test
import pytest
from httpx import AsyncClient
from app.main import app

@pytest.mark.asyncio
class TestExamFlow:
    async def test_complete_exam_flow(self):
        async with AsyncClient(app=app, base_url="http://test") as client:
            # Given: Create tenant and user
            tenant_response = await client.post("/tenants", json={
                "name": "Test Tenant",
                "plan": "basic"
            })
            assert tenant_response.status_code == 201
            tenant_id = tenant_response.json()["tenant_id"]

            # Create authenticated user
            auth_response = await client.post("/auth/login", json={
                "email": "<EMAIL>",
                "password": "password123"
            })
            token = auth_response.json()["access_token"]
            headers = {"Authorization": f"Bearer {token}"}

            # When: Create exam
            exam_response = await client.post("/exams",
                json={
                    "title": "Integration Test Exam",
                    "time_limit_sec": 3600
                },
                headers=headers
            )
            assert exam_response.status_code == 201
            exam_id = exam_response.json()["exam_id"]

            # Start exam attempt
            attempt_response = await client.post("/exam-attempts",
                json={"exam_id": exam_id},
                headers=headers
            )
            assert attempt_response.status_code == 201
            attempt_id = attempt_response.json()["attempt_id"]

            # Submit answers
            answer_response = await client.post("/answers",
                json={
                    "attempt_id": attempt_id,
                    "question_id": "q1",
                    "answer_data": {"text": "Test answer"}
                },
                headers=headers
            )
            assert answer_response.status_code == 201

            # Finish exam
            finish_response = await client.post(f"/attempts/{attempt_id}/finish",
                headers=headers
            )
            assert finish_response.status_code == 200

            # Then: Verify results
            result = finish_response.json()
            assert result["status"] == "completed"
            assert "total_score" in result
```

### 2.3 End-to-End Testing

#### Cypress E2E Tests
```typescript
// cypress/e2e/exam-taking.cy.ts
describe('Exam Taking Flow', () => {
  beforeEach(() => {
    // Setup test data
    cy.task('seedDatabase');
    cy.login('<EMAIL>', 'password123');
  });

  it('should complete a full exam successfully', () => {
    // Given: Navigate to exam
    cy.visit('/exams/placement-test');
    cy.contains('English Placement Test').should('be.visible');

    // When: Start exam
    cy.get('[data-cy=start-exam-btn]').click();

    // Verify timer starts
    cy.get('[data-cy=timer]').should('contain', '59:');

    // Answer writing question
    cy.get('[data-cy=question-prompt]').should('contain', 'Describe');
    cy.get('[data-cy=answer-textarea]').type('This is my test answer for the writing question.');

    // Navigate to next question
    cy.get('[data-cy=next-question-btn]').click();

    // Answer listening question
    cy.get('[data-cy=audio-player]').should('be.visible');
    cy.get('[data-cy=play-audio-btn]').click();
    cy.wait(5000); // Wait for audio

    cy.get('[data-cy=option-a]').click();
    cy.get('[data-cy=next-question-btn]').click();

    // Answer speaking question
    cy.get('[data-cy=record-btn]').click();
    cy.wait(3000); // Simulate recording
    cy.get('[data-cy=stop-record-btn]').click();

    // Submit exam
    cy.get('[data-cy=finish-exam-btn]').click();
    cy.get('[data-cy=confirm-submit-btn]').click();

    // Then: Verify completion
    cy.url().should('include', '/exam-completed');
    cy.contains('Exam submitted successfully').should('be.visible');
    cy.contains('Results will be available').should('be.visible');
  });

  it('should handle exam timeout gracefully', () => {
    cy.visit('/exams/timed-test');
    cy.get('[data-cy=start-exam-btn]').click();

    // Simulate time running out
    cy.clock();
    cy.tick(3600000); // 1 hour

    // Verify auto-submission
    cy.contains('Time expired').should('be.visible');
    cy.url().should('include', '/exam-completed');
  });

  it('should save answers automatically', () => {
    cy.visit('/exams/placement-test');
    cy.get('[data-cy=start-exam-btn]').click();

    // Type answer
    cy.get('[data-cy=answer-textarea]').type('Auto-save test answer');

    // Verify auto-save indicator
    cy.get('[data-cy=autosave-indicator]').should('contain', 'Saved');

    // Refresh page to test persistence
    cy.reload();
    cy.get('[data-cy=answer-textarea]').should('contain.value', 'Auto-save test answer');
  });
});
```

---

## 3. Performance Testing

### 3.1 Load Testing con k6

```javascript
// load-test.js
import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate } from 'k6/metrics';

export let errorRate = new Rate('errors');

export let options = {
  stages: [
    { duration: '2m', target: 100 }, // Ramp up
    { duration: '5m', target: 100 }, // Stay at 100 users
    { duration: '2m', target: 200 }, // Ramp up to 200
    { duration: '5m', target: 200 }, // Stay at 200
    { duration: '2m', target: 0 },   // Ramp down
  ],
  thresholds: {
    http_req_duration: ['p(95)<200'], // 95% of requests under 200ms
    http_req_failed: ['rate<0.1'],    // Error rate under 10%
    errors: ['rate<0.1'],
  },
};

const BASE_URL = 'https://api.arroyo.app';

export function setup() {
  // Login and get token
  let loginRes = http.post(`${BASE_URL}/auth/login`, {
    email: '<EMAIL>',
    password: 'loadtest123'
  });

  return { token: loginRes.json('access_token') };
}

export default function(data) {
  let headers = {
    'Authorization': `Bearer ${data.token}`,
    'Content-Type': 'application/json',
  };

  // Test API endpoints
  let responses = http.batch([
    ['GET', `${BASE_URL}/courses`, null, { headers }],
    ['GET', `${BASE_URL}/questions?limit=10`, null, { headers }],
    ['GET', `${BASE_URL}/exams`, null, { headers }],
  ]);

  responses.forEach((res) => {
    let success = check(res, {
      'status is 200': (r) => r.status === 200,
      'response time < 200ms': (r) => r.timings.duration < 200,
    });

    errorRate.add(!success);
  });

  sleep(1);
}

export function teardown(data) {
  // Cleanup if needed
}
```

### 3.2 Database Performance Testing

```bash
#!/bin/bash
# db-performance-test.sh

echo "🗄️ Running database performance tests..."

# Connection test
pgbench -h $DB_HOST -U $DB_USER -d $DB_NAME -c 10 -j 2 -T 60 -r

# Custom test for exam queries
pgbench -h $DB_HOST -U $DB_USER -d $DB_NAME -c 5 -j 2 -T 30 -f exam_queries.sql

# Multi-tenant performance test
pgbench -h $DB_HOST -U $DB_USER -d $DB_NAME -c 20 -j 4 -T 120 -f tenant_isolation.sql

echo "✅ Database performance tests completed"
```

---

## 4. Security Testing

### 4.1 Automated Security Scanning

```yaml
# .github/workflows/security.yml
name: Security Scan

on: [push, pull_request]

jobs:
  security:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3

    - name: Run Bandit Security Scan
      run: |
        pip install bandit
        bandit -r . -f json -o bandit-report.json

    - name: Run Safety Check
      run: |
        pip install safety
        safety check --json --output safety-report.json

    - name: OWASP ZAP Baseline Scan
      uses: zaproxy/action-baseline@v0.7.0
      with:
        target: 'https://staging.arroyo.app'
        rules_file_name: '.zap/rules.tsv'

    - name: Upload Security Reports
      uses: actions/upload-artifact@v3
      with:
        name: security-reports
        path: |
          bandit-report.json
          safety-report.json
          report_html.html
```

### 4.2 Penetration Testing Checklist

| Test Category | Test Cases | Tools | Frequency |
|---------------|------------|-------|-----------|
| **Authentication** | Brute force, session hijacking | Burp Suite | Monthly |
| **Authorization** | Privilege escalation, RBAC bypass | Custom scripts | Monthly |
| **Input Validation** | SQL injection, XSS, CSRF | OWASP ZAP | Weekly |
| **API Security** | Rate limiting, JWT validation | Postman, curl | Weekly |
| **Infrastructure** | Network scanning, SSL/TLS | Nmap, testssl.sh | Monthly |

---

## 5. Quality Gates y CI/CD

### 5.1 Quality Gates Configuration

```yaml
# sonar-project.properties
sonar.projectKey=arroyo-university
sonar.organization=arroyo-code

# Quality Gate thresholds
sonar.qualitygate.wait=true
sonar.coverage.exclusions=**/tests/**,**/migrations/**
sonar.python.coverage.reportPaths=coverage.xml
sonar.javascript.lcov.reportPaths=coverage/lcov.info

# Quality thresholds
sonar.coverage.minimum=80
sonar.duplicated_lines_density.maximum=3
sonar.maintainability_rating.minimum=A
sonar.reliability_rating.minimum=A
sonar.security_rating.minimum=A
```

### 5.2 CI/CD Pipeline con Quality Gates

```yaml
# .github/workflows/ci-cd.yml
name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3

    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install -r requirements-dev.txt

    - name: Run unit tests
      run: |
        pytest --cov=. --cov-report=xml --cov-report=html

    - name: Run integration tests
      run: |
        pytest tests/integration/ -v

    - name: Frontend tests
      run: |
        cd frontend
        npm ci
        npm run test:coverage

    - name: SonarCloud Scan
      uses: SonarSource/sonarcloud-github-action@master
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

    - name: Quality Gate Check
      run: |
        # Fail if quality gate fails
        if [ "${{ steps.sonarcloud.outputs.quality-gate-status }}" != "PASSED" ]; then
          echo "Quality gate failed"
          exit 1
        fi

  security:
    needs: test
    runs-on: ubuntu-latest
    steps:
    - name: Security scan
      run: |
        bandit -r . -f json
        safety check

  deploy:
    needs: [test, security]
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    steps:
    - name: Deploy to staging
      run: |
        # Deployment logic
        echo "Deploying to staging..."
```

---

## 6. Test Data Management

### 6.1 Test Data Strategy

```python
# conftest.py - Pytest fixtures
import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.database import Base
from app.models import *

@pytest.fixture(scope="session")
def test_db():
    engine = create_engine("postgresql://test:test@localhost/test_arroyo")
    Base.metadata.create_all(engine)
    yield engine
    Base.metadata.drop_all(engine)

@pytest.fixture
def db_session(test_db):
    Session = sessionmaker(bind=test_db)
    session = Session()
    yield session
    session.rollback()
    session.close()

@pytest.fixture
def sample_tenant(db_session):
    tenant = Tenant(
        name="Test Tenant",
        plan="basic",
        ia_tokens_used=0
    )
    db_session.add(tenant)
    db_session.commit()
    return tenant

@pytest.fixture
def sample_user(db_session, sample_tenant):
    user = User(
        tenant_id=sample_tenant.tenant_id,
        email="<EMAIL>",
        username="testuser",
        password_hash="hashed_password",
        verified=True
    )
    db_session.add(user)
    db_session.commit()
    return user

@pytest.fixture
def sample_exam_with_questions(db_session, sample_tenant):
    # Create exam
    exam = Exam(
        tenant_id=sample_tenant.tenant_id,
        title="Sample Exam",
        time_limit_sec=3600
    )
    db_session.add(exam)

    # Create questions
    questions = [
        TestItem(
            tenant_id=sample_tenant.tenant_id,
            item_type="writing",
            prompt="Describe your hometown",
            difficulty="B1"
        ),
        TestItem(
            tenant_id=sample_tenant.tenant_id,
            item_type="listening",
            prompt="Listen to the conversation",
            difficulty="B1",
            metadata={"audio_url": "test.mp3"}
        )
    ]

    for q in questions:
        db_session.add(q)

    db_session.commit()
    return exam, questions
```

---

## 7. Accessibility Testing

### 7.1 Automated Accessibility Testing

```typescript
// accessibility.test.ts
import { render } from '@testing-library/react';
import { axe, toHaveNoViolations } from 'jest-axe';
import { ExamInterface } from './ExamInterface';

expect.extend(toHaveNoViolations);

describe('Accessibility Tests', () => {
  test('ExamInterface should have no accessibility violations', async () => {
    const { container } = render(<ExamInterface />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  test('should support keyboard navigation', () => {
    render(<ExamInterface />);

    // Test tab navigation
    const firstButton = screen.getByRole('button', { name: /start exam/i });
    firstButton.focus();
    expect(firstButton).toHaveFocus();

    // Test Enter key activation
    fireEvent.keyDown(firstButton, { key: 'Enter' });
    expect(mockStartExam).toHaveBeenCalled();
  });

  test('should have proper ARIA labels', () => {
    render(<ExamInterface />);

    expect(screen.getByRole('main')).toHaveAttribute('aria-label', 'Exam interface');
    expect(screen.getByRole('timer')).toHaveAttribute('aria-live', 'polite');
    expect(screen.getByRole('progressbar')).toHaveAttribute('aria-valuenow');
  });
});
```

---

## 8. Monitoring y Alerting de Calidad

### 8.1 Quality Metrics Dashboard

```python
# quality_metrics.py
class QualityMetrics:
    def __init__(self):
        self.prometheus = PrometheusClient()

    def track_test_metrics(self, test_results):
        # Track test coverage
        self.prometheus.gauge('test_coverage_percentage').set(test_results.coverage)

        # Track test execution time
        self.prometheus.histogram('test_execution_duration').observe(test_results.duration)

        # Track test failures
        self.prometheus.counter('test_failures_total').inc(test_results.failures)

        # Track quality gate status
        self.prometheus.gauge('quality_gate_status').set(1 if test_results.quality_gate_passed else 0)

    def track_production_quality(self, metrics):
        # Track error rates
        self.prometheus.gauge('error_rate_percentage').set(metrics.error_rate)

        # Track performance
        self.prometheus.histogram('response_time_p95').observe(metrics.p95_response_time)

        # Track user satisfaction
        self.prometheus.gauge('user_satisfaction_score').set(metrics.nps_score)
```

### 8.2 Quality Alerts

```yaml
# quality-alerts.yml
groups:
- name: quality.rules
  rules:
  - alert: HighTestFailureRate
    expr: test_failures_total / test_executions_total > 0.1
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High test failure rate detected"

  - alert: QualityGateFailed
    expr: quality_gate_status == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "Quality gate failed - blocking deployment"

  - alert: CoverageDropped
    expr: test_coverage_percentage < 80
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "Test coverage dropped below 80%"
```

---

## Conclusión

Esta estrategia integral de testing y QA asegura que Arroyo University mantenga los más altos estándares de calidad, seguridad y confiabilidad. La combinación de testing automatizado, quality gates, monitoreo continuo y procesos de mejora garantiza la entrega de software robusto que cumple con las expectativas de usuarios y stakeholders.