<!DOCTYPE html>
<html lang="es" class="h-full bg-gray-50">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crear Examen - Arroyo University</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
    </style>
</head>
<body class="h-full">
    <div class="min-h-full">
        <!-- Navigation -->
        <nav class="bg-white shadow">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.84L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3z"/>
                            </svg>
                        </div>
                        <h1 class="ml-3 text-xl font-semibold text-gray-900">Universidad Ejemplo</h1>
                        <nav class="ml-8 flex space-x-4">
                            <a href="05_content_creator_dashboard.html" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">Dashboard</a>
                            <a href="08_course_creation.html" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">Cursos</a>
                            <a href="09_question_bank.html" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">Preguntas</a>
                            <a href="#" class="bg-blue-100 text-blue-700 px-3 py-2 rounded-md text-sm font-medium">Exámenes</a>
                        </nav>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                            <span class="text-sm font-medium text-gray-700">MG</span>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="mb-8">
                <h2 class="text-2xl font-bold text-gray-900">Crear Nuevo Examen</h2>
                <p class="mt-1 text-gray-600">Configura un examen seleccionando preguntas y estableciendo parámetros</p>
            </div>

            <!-- Progress Steps -->
            <div class="mb-8">
                <div class="flex items-center">
                    <div class="flex items-center text-blue-600">
                        <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                            <span class="text-white text-sm font-medium">1</span>
                        </div>
                        <span class="ml-2 text-sm font-medium">Información Básica</span>
                    </div>
                    <div class="flex-1 h-px bg-gray-300 mx-4"></div>
                    <div class="flex items-center text-gray-400">
                        <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                            <span class="text-white text-sm font-medium">2</span>
                        </div>
                        <span class="ml-2 text-sm font-medium">Seleccionar Preguntas</span>
                    </div>
                    <div class="flex-1 h-px bg-gray-300 mx-4"></div>
                    <div class="flex items-center text-gray-400">
                        <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                            <span class="text-white text-sm font-medium">3</span>
                        </div>
                        <span class="ml-2 text-sm font-medium">Configuración</span>
                    </div>
                </div>
            </div>

            <!-- Step 1: Basic Information -->
            <div id="step1" class="bg-white shadow rounded-lg">
                <div class="px-6 py-8">
                    <h3 class="text-lg font-medium text-gray-900 mb-6">Información del Examen</h3>
                    
                    <div class="space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="md:col-span-2">
                                <label for="exam-title" class="block text-sm font-medium text-gray-700 mb-2">
                                    Título del Examen *
                                </label>
                                <input type="text" id="exam-title" required
                                       placeholder="English B2 Assessment - Midterm"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            </div>
                            
                            <div>
                                <label for="course-select" class="block text-sm font-medium text-gray-700 mb-2">
                                    Curso *
                                </label>
                                <select id="course-select" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">Seleccionar curso</option>
                                    <option value="english-b2" selected>English B2 - Intermediate High</option>
                                    <option value="advanced-grammar">Advanced Grammar</option>
                                    <option value="business-english">Business English</option>
                                </select>
                            </div>
                            
                            <div>
                                <label for="exam-type" class="block text-sm font-medium text-gray-700 mb-2">
                                    Tipo de Examen
                                </label>
                                <select id="exam-type" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="assessment" selected>Evaluación</option>
                                    <option value="practice">Práctica</option>
                                    <option value="certification">Certificación</option>
                                    <option value="placement">Nivelación</option>
                                </select>
                            </div>
                            
                            <div class="md:col-span-2">
                                <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                                    Descripción
                                </label>
                                <textarea id="description" rows="3"
                                          placeholder="Describe el propósito y contenido del examen..."
                                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"></textarea>
                            </div>
                        </div>

                        <!-- Exam Settings -->
                        <div class="border-t pt-6">
                            <h4 class="text-md font-medium text-gray-900 mb-4">Configuración Básica</h4>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div>
                                    <label for="time-limit" class="block text-sm font-medium text-gray-700 mb-2">
                                        Tiempo límite (minutos)
                                    </label>
                                    <input type="number" id="time-limit" min="15" max="300" value="90"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                
                                <div>
                                    <label for="passing-score" class="block text-sm font-medium text-gray-700 mb-2">
                                        Puntuación mínima (%)
                                    </label>
                                    <input type="number" id="passing-score" min="0" max="100" value="70"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                
                                <div>
                                    <label for="attempts" class="block text-sm font-medium text-gray-700 mb-2">
                                        Intentos permitidos
                                    </label>
                                    <select id="attempts" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="1" selected>1 intento</option>
                                        <option value="2">2 intentos</option>
                                        <option value="3">3 intentos</option>
                                        <option value="unlimited">Ilimitados</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Availability -->
                        <div class="border-t pt-6">
                            <h4 class="text-md font-medium text-gray-900 mb-4">Disponibilidad</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="start-date" class="block text-sm font-medium text-gray-700 mb-2">
                                        Fecha de inicio
                                    </label>
                                    <input type="datetime-local" id="start-date"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                
                                <div>
                                    <label for="end-date" class="block text-sm font-medium text-gray-700 mb-2">
                                        Fecha de cierre
                                    </label>
                                    <input type="datetime-local" id="end-date"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                </div>
                            </div>
                        </div>

                        <!-- Question Selection Preview -->
                        <div class="border-t pt-6">
                            <h4 class="text-md font-medium text-gray-900 mb-4">Preguntas Disponibles</h4>
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                                    <div>
                                        <div class="text-2xl font-bold text-blue-600">23</div>
                                        <div class="text-sm text-blue-800">Writing</div>
                                    </div>
                                    <div>
                                        <div class="text-2xl font-bold text-green-600">18</div>
                                        <div class="text-sm text-green-800">Listening</div>
                                    </div>
                                    <div>
                                        <div class="text-2xl font-bold text-purple-600">15</div>
                                        <div class="text-sm text-purple-800">Speaking</div>
                                    </div>
                                    <div>
                                        <div class="text-2xl font-bold text-yellow-600">12</div>
                                        <div class="text-sm text-yellow-800">Reading</div>
                                    </div>
                                </div>
                                <p class="text-center text-sm text-blue-700 mt-3">
                                    Total: 68 preguntas disponibles en el curso seleccionado
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Navigation -->
                    <div class="flex justify-between pt-8 border-t">
                        <button onclick="window.location.href='05_content_creator_dashboard.html'" 
                                class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 font-medium">
                            Cancelar
                        </button>
                        <button onclick="nextStep()" 
                                class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 font-medium">
                            Siguiente: Seleccionar Preguntas
                        </button>
                    </div>
                </div>
            </div>

            <!-- Step 2: Question Selection (Hidden initially) -->
            <div id="step2" class="bg-white shadow rounded-lg hidden">
                <div class="px-6 py-8">
                    <h3 class="text-lg font-medium text-gray-900 mb-6">Seleccionar Preguntas</h3>
                    
                    <!-- Quick Selection Options -->
                    <div class="mb-6">
                        <h4 class="text-md font-medium text-gray-900 mb-4">Selección Rápida</h4>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <button onclick="autoSelect('balanced')" 
                                    class="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left">
                                <h5 class="font-medium text-gray-900">Balanceado</h5>
                                <p class="text-sm text-gray-600 mt-1">25 preguntas (6W, 7L, 6S, 6R)</p>
                            </button>
                            <button onclick="autoSelect('comprehensive')" 
                                    class="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left">
                                <h5 class="font-medium text-gray-900">Comprensivo</h5>
                                <p class="text-sm text-gray-600 mt-1">40 preguntas (10W, 12L, 10S, 8R)</p>
                            </button>
                            <button onclick="autoSelect('quick')" 
                                    class="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left">
                                <h5 class="font-medium text-gray-900">Rápido</h5>
                                <p class="text-sm text-gray-600 mt-1">15 preguntas (4W, 5L, 3S, 3R)</p>
                            </button>
                        </div>
                    </div>

                    <!-- Selected Questions Summary -->
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                        <h4 class="text-md font-medium text-green-900 mb-2">Preguntas Seleccionadas</h4>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                            <div>
                                <div class="text-xl font-bold text-green-600" id="selected-writing">6</div>
                                <div class="text-sm text-green-800">Writing</div>
                            </div>
                            <div>
                                <div class="text-xl font-bold text-green-600" id="selected-listening">7</div>
                                <div class="text-sm text-green-800">Listening</div>
                            </div>
                            <div>
                                <div class="text-xl font-bold text-green-600" id="selected-speaking">6</div>
                                <div class="text-sm text-green-800">Speaking</div>
                            </div>
                            <div>
                                <div class="text-xl font-bold text-green-600" id="selected-reading">6</div>
                                <div class="text-sm text-green-800">Reading</div>
                            </div>
                        </div>
                        <p class="text-center text-sm text-green-700 mt-3">
                            Total: <span id="total-selected">25</span> preguntas • Tiempo estimado: <span id="estimated-time">90</span> minutos
                        </p>
                    </div>

                    <!-- Navigation -->
                    <div class="flex justify-between pt-6 border-t">
                        <button onclick="prevStep()" 
                                class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 font-medium">
                            Anterior
                        </button>
                        <button onclick="nextStep()" 
                                class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 font-medium">
                            Siguiente: Configuración Final
                        </button>
                    </div>
                </div>
            </div>

            <!-- Step 3: Final Configuration (Hidden initially) -->
            <div id="step3" class="bg-white shadow rounded-lg hidden">
                <div class="px-6 py-8">
                    <h3 class="text-lg font-medium text-gray-900 mb-6">Configuración Final</h3>
                    
                    <!-- Exam Summary -->
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
                        <h4 class="text-lg font-medium text-blue-900 mb-4">Resumen del Examen</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h5 class="text-sm font-medium text-blue-800 mb-2">Información General</h5>
                                <ul class="text-sm text-blue-700 space-y-1">
                                    <li>• Título: English B2 Assessment - Midterm</li>
                                    <li>• Curso: English B2 - Intermediate High</li>
                                    <li>• Tipo: Evaluación</li>
                                    <li>• Tiempo límite: 90 minutos</li>
                                </ul>
                            </div>
                            <div>
                                <h5 class="text-sm font-medium text-blue-800 mb-2">Preguntas</h5>
                                <ul class="text-sm text-blue-700 space-y-1">
                                    <li>• Writing: 6 preguntas</li>
                                    <li>• Listening: 7 preguntas</li>
                                    <li>• Speaking: 6 preguntas</li>
                                    <li>• Reading: 6 preguntas</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Publish Options -->
                    <div class="space-y-6">
                        <div>
                            <h4 class="text-md font-medium text-gray-900 mb-4">Opciones de Publicación</h4>
                            <div class="space-y-3">
                                <label class="flex items-center">
                                    <input type="radio" name="publish-option" value="draft" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300" checked>
                                    <span class="ml-3 text-gray-700">Guardar como borrador</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="publish-option" value="schedule" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                                    <span class="ml-3 text-gray-700">Programar publicación</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="publish-option" value="immediate" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                                    <span class="ml-3 text-gray-700">Publicar inmediatamente</span>
                                </label>
                            </div>
                        </div>

                        <div>
                            <h4 class="text-md font-medium text-gray-900 mb-4">Notificaciones</h4>
                            <div class="space-y-3">
                                <label class="flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" checked>
                                    <span class="ml-3 text-gray-700">Notificar a estudiantes por email</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                    <span class="ml-3 text-gray-700">Enviar recordatorio 24h antes del cierre</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Navigation -->
                    <div class="flex justify-between pt-8 border-t">
                        <button onclick="prevStep()" 
                                class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 font-medium">
                            Anterior
                        </button>
                        <button onclick="createExam()" 
                                class="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 font-medium">
                            Crear Examen
                        </button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        let currentStep = 1;

        function nextStep() {
            if (currentStep < 3) {
                document.getElementById(`step${currentStep}`).classList.add('hidden');
                currentStep++;
                document.getElementById(`step${currentStep}`).classList.remove('hidden');
                updateProgressSteps();
            }
        }

        function prevStep() {
            if (currentStep > 1) {
                document.getElementById(`step${currentStep}`).classList.add('hidden');
                currentStep--;
                document.getElementById(`step${currentStep}`).classList.remove('hidden');
                updateProgressSteps();
            }
        }

        function updateProgressSteps() {
            for (let i = 1; i <= 3; i++) {
                const step = document.querySelector(`.flex.items-center:nth-child(${i * 2 - 1})`);
                if (i <= currentStep) {
                    step.className = step.className.replace('text-gray-400', 'text-blue-600');
                    step.querySelector('.w-8.h-8').className = 'w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center';
                } else {
                    step.className = step.className.replace('text-blue-600', 'text-gray-400');
                    step.querySelector('.w-8.h-8').className = 'w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center';
                }
            }
        }

        function autoSelect(type) {
            const selections = {
                balanced: { writing: 6, listening: 7, speaking: 6, reading: 6 },
                comprehensive: { writing: 10, listening: 12, speaking: 10, reading: 8 },
                quick: { writing: 4, listening: 5, speaking: 3, reading: 3 }
            };

            const selection = selections[type];
            document.getElementById('selected-writing').textContent = selection.writing;
            document.getElementById('selected-listening').textContent = selection.listening;
            document.getElementById('selected-speaking').textContent = selection.speaking;
            document.getElementById('selected-reading').textContent = selection.reading;

            const total = Object.values(selection).reduce((a, b) => a + b, 0);
            document.getElementById('total-selected').textContent = total;
            document.getElementById('estimated-time').textContent = Math.round(total * 3.5);
        }

        function createExam() {
            alert('¡Examen creado exitosamente!\n\nEl examen "English B2 Assessment - Midterm" ha sido guardado como borrador.\nPuedes publicarlo cuando estés listo.');
            window.location.href = '05_content_creator_dashboard.html';
        }

        // Initialize with balanced selection
        autoSelect('balanced');
    </script>
</body>
</html>
