# Arquitectura del Sistema - Arroyo University

## Introducción

Este documento describe la arquitectura técnica de alto nivel de Arroyo University, incluyendo componentes, tecnologías, patrones de diseño y decisiones arquitectónicas clave. La arquitectura está diseñada para soportar multi-tenancy, escalabilidad horizontal y integración de servicios de IA.

---

## 1. Visión Arquitectónica

### 1.1 Principios de Diseño
- **Multi-tenant nativo**: Aislamiento seguro por organización
- **Microservicios**: Separación de responsabilidades y escalabilidad independiente
- **API-first**: Todas las funcionalidades expuestas vía APIs RESTful
- **Cloud-native**: Diseñado para contenedores y orquestación Kubernetes
- **Observabilidad**: Logging, métricas y trazas distribuidas integradas

### 1.2 Patrones Arquitectónicos
- **Clean Architecture**: Separación clara entre capas de negocio y técnicas
- **CQRS**: Separación de comandos y consultas para optimización
- **Event Sourcing**: Para auditoría y trazabilidad de cambios críticos
- **Circuit Breaker**: Resiliencia ante fallos de servicios externos
- **Bulkhead**: Aislamiento de recursos por tenant

---

## 2. Arquitectura de Alto Nivel

### 2.1 Componentes Principales

| Capa           | Componente          | Tecnología                                                      | Responsabilidad                                                        |
| -------------- | ------------------- | --------------------------------------------------------------- | ---------------------------------------------------------------------- |
| **UI**         | Frontend SPA        | React 18, Vite, Tailwind, React Query, i18next                 | SPA, auth, grabación y reproducción audio, dashboards, PWA offline    |
| **API Gateway**| Kong/Nginx          | Kong Gateway, rate limiting, auth                               | Routing, rate limiting, auth, SSL termination                         |
| **Lógica**     | Core API            | Python 3.11, FastAPI, SQLModel, Alembic                        | CRUD, RBAC, multi‑tenant enforcement, pagos, notificaciones           |
| **IA**         | AI Service          | Python 3.11, FastAPI async, Celery                             | Orquestación prompts, moderación, scoring rubric, caching             |
| **Persistencia** | Database          | PostgreSQL 15 + citext, pg_net, pgvector                       | Datos transaccionales, embeddings semánticos, funciones notificadoras |
| **Mensajería** | Message Queue       | Redis 7 + Celery, (futuro) Kafka                               | Jobs IA, mailing, webhooks, facturación asíncrona                     |
| **Archivos**   | Object Storage      | Azure Blob + CDN                                                | Audios, PDF, media; presigned URLs                                    |
| **Observabilidad** | Telemetry       | Prometheus, Grafana, Loki, Sentry, OpenTelemetry               | Logs, métricas, trazas distribuidas, alerting                         |
| **Infraestructura** | Container Platform | Docker, AKS, Terraform IaC, GitHub Actions                   | Build, test, deploy, canary releases, blue‑green db migrations        |

### 2.2 Diagrama de Arquitectura

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Browser   │    │   Mobile App    │    │  External APIs  │
│   (React SPA)   │    │     (PWA)       │    │   (LMS/HRIS)    │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────┴─────────────┐
                    │      API Gateway          │
                    │   (Kong/Rate Limiting)    │
                    └─────────────┬─────────────┘
                                  │
          ┌───────────────────────┼───────────────────────┐
          │                       │                       │
    ┌─────┴─────┐         ┌───────┴───────┐       ┌───────┴───────┐
    │ Core API  │         │  AI Service   │       │ Notification  │
    │ (FastAPI) │         │  (FastAPI)    │       │   Service     │
    └─────┬─────┘         └───────┬───────┘       └───────┬───────┘
          │                       │                       │
          └───────────────────────┼───────────────────────┘
                                  │
                    ┌─────────────┴─────────────┐
                    │     Message Queue         │
                    │    (Redis + Celery)       │
                    └─────────────┬─────────────┘
                                  │
          ┌───────────────────────┼───────────────────────┐
          │                       │                       │
    ┌─────┴─────┐         ┌───────┴───────┐       ┌───────┴───────┐
    │PostgreSQL │         │ Object Storage│       │ Observability │
    │ (Primary) │         │ (Azure Blob)  │       │   Stack       │
    └───────────┘         └───────────────┘       └───────────────┘
```

---

## 3. Componentes Detallados

### 3.1 Frontend SPA

#### Tecnologías
- **React 18+**: Framework principal con Concurrent Features
- **Vite**: Build tool y dev server optimizado
- **Tailwind CSS v4**: Utility-first CSS framework
- **React Query**: State management y caching de datos
- **i18next**: Internacionalización completa

#### Características
- **PWA**: Service Workers para funcionalidad offline
- **Responsive**: Mobile-first design
- **Accesibilidad**: WCAG 2.1 AA compliance
- **Audio/Video**: Grabación y reproducción nativa
- **Real-time**: WebSocket para notificaciones

### 3.2 Core API

#### Tecnologías
- **FastAPI**: Framework async de alto rendimiento
- **SQLModel**: ORM type-safe basado en Pydantic
- **Alembic**: Migraciones de base de datos
- **Pydantic**: Validación y serialización de datos

#### Responsabilidades
- CRUD operations para todas las entidades
- Enforcement de multi-tenancy
- Autenticación y autorización (RBAC)
- Orquestación de workflows de negocio
- Integración con servicios externos

#### Patrones Implementados
```python
# Ejemplo de estructura Clean Architecture
src/
├── domain/          # Entidades y reglas de negocio
├── application/     # Casos de uso y servicios
├── infrastructure/  # Adaptadores externos
└── presentation/    # Controllers y schemas
```

### 3.3 AI Service

#### Tecnologías
- **FastAPI**: API async para servicios IA
- **Celery**: Procesamiento asíncrono de jobs
- **OpenAI SDK**: Integración con GPT-4
- **Azure Speech SDK**: TTS y STT

#### Responsabilidades
- Generación de preguntas por tipo (Writing, Listening, Speaking)
- Scoring automático de respuestas
- Moderación de contenido
- Síntesis y reconocimiento de voz
- Detección de plagio

#### Arquitectura de Jobs
```python
# Ejemplo de job de generación
@celery_app.task
def generate_questions_task(tenant_id: str, prompt: str, count: int):
    # 1. Validar cuota IA
    # 2. Generar contenido con LLM
    # 3. Procesar y almacenar resultados
    # 4. Notificar completación
    pass
```

### 3.4 Base de Datos

#### PostgreSQL 15+ Features
- **Row-Level Security (RLS)**: Aislamiento automático por tenant
- **JSONB**: Almacenamiento flexible de metadata
- **pgvector**: Búsquedas semánticas con embeddings
- **Partitioning**: Escalabilidad horizontal por tenant

#### Estrategia Multi-Tenant
- **Shared Database, Shared Schema**: Todas las tablas incluyen `tenant_id`
- **RLS Policies**: Filtrado automático por tenant
- **Connection Pooling**: PgBouncer para optimización de conexiones

---

## 4. Seguridad y Compliance

### 4.1 Autenticación y Autorización
- **JWT Tokens**: Stateless authentication
- **MFA**: TOTP support (RFC 6238)
- **RBAC**: Role-based access control granular
- **SSO**: SAML 2.0 y OIDC support

### 4.2 Seguridad de Datos
- **Encryption at Rest**: Database y object storage
- **Encryption in Transit**: TLS 1.3 end-to-end
- **Data Isolation**: RLS y tenant boundaries
- **Audit Logging**: Todas las operaciones críticas

### 4.3 Compliance
- **GDPR/CCPA**: Data subject rights, retention policies
- **FERPA**: Educational records protection
- **OWASP Top 10**: Security best practices
- **SOC 2**: Controls framework

---

## 5. Escalabilidad y Performance

### 5.1 Estrategias de Escalabilidad
- **Horizontal Pod Autoscaling (HPA)**: Basado en CPU/memoria
- **Vertical Pod Autoscaling (VPA)**: Optimización de recursos
- **Database Read Replicas**: Distribución de carga de lectura
- **CDN**: Distribución global de assets estáticos

### 5.2 Optimizaciones de Performance
- **Caching**: Redis para sesiones y datos frecuentes
- **Database Indexing**: Índices compuestos por tenant_id
- **Query Optimization**: Análisis y optimización continua
- **Asset Optimization**: Compresión y lazy loading

### 5.3 Targets de Performance
- **API Response Time**: p95 < 200ms
- **Database Queries**: p95 < 50ms
- **AI Generation**: < 15s end-to-end
- **Page Load Time**: LCP < 2.5s

---

## 6. Observabilidad y Monitoreo

### 6.1 Stack de Observabilidad
- **Prometheus**: Métricas y alerting
- **Grafana**: Dashboards y visualización
- **Loki**: Centralized logging
- **Sentry**: Error tracking y performance
- **OpenTelemetry**: Distributed tracing

### 6.2 Métricas Clave
- **Business**: Usuarios activos, exámenes completados
- **Technical**: Latencia, throughput, error rates
- **Infrastructure**: CPU, memoria, disk, network
- **Security**: Failed logins, suspicious activity

### 6.3 Alerting
- **SLO Violations**: Availability, latency, error rate
- **Resource Exhaustion**: CPU, memory, disk space
- **Security Events**: Multiple failed logins, unusual patterns
- **Business Critical**: Payment failures, data corruption

---

## 7. Deployment y DevOps

### 7.1 Infraestructura como Código
- **Terraform**: Provisioning de recursos cloud
- **Helm Charts**: Deployment de aplicaciones Kubernetes
- **GitOps**: Flux para continuous deployment

### 7.2 CI/CD Pipeline
```yaml
# Ejemplo de pipeline
stages:
  - test          # Unit tests, integration tests
  - security      # SAST, dependency scanning
  - build         # Docker images, artifacts
  - deploy-dev    # Automatic deployment to dev
  - deploy-staging # Manual approval required
  - deploy-prod   # Blue-green deployment
```

### 7.3 Estrategias de Deployment
- **Blue-Green**: Zero-downtime deployments
- **Canary**: Gradual rollout con monitoring
- **Feature Flags**: A/B testing y rollback rápido

---

## 8. Decisiones Arquitectónicas

### 8.1 ADR-001: Multi-Tenant Strategy
**Decisión**: Shared Database, Shared Schema con RLS
**Razón**: Balance entre aislamiento y eficiencia operacional
**Alternativas**: Database per tenant, Schema per tenant

### 8.2 ADR-002: AI Service Architecture
**Decisión**: Microservicio separado con Celery
**Razón**: Aislamiento de recursos y escalabilidad independiente
**Alternativas**: Integración directa en Core API

### 8.3 ADR-003: Frontend Architecture
**Decisión**: SPA con PWA capabilities
**Razón**: Experiencia de usuario moderna y funcionalidad offline
**Alternativas**: Server-side rendering, Native mobile apps

---

## Conclusión

La arquitectura de Arroyo University está diseñada para soportar los requisitos actuales mientras proporciona flexibilidad para el crecimiento futuro. La combinación de tecnologías modernas, patrones probados y principios de diseño sólidos asegura escalabilidad, mantenibilidad y extensibilidad a largo plazo.
