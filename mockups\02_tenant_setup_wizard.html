<!DOCTYPE html>
<html lang="es" class="h-full bg-gray-50">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Configuración Inicial - Arroyo University</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
    </style>
</head>
<body class="h-full">
    <div class="min-h-full">
        <!-- Header -->
        <header class="bg-white shadow">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-6">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.84L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3z"/>
                            </svg>
                        </div>
                        <h1 class="ml-3 text-xl font-semibold text-gray-900">Arroyo University</h1>
                    </div>
                    <div class="text-sm text-gray-500">
                        Paso 2 de 4
                    </div>
                </div>
            </div>
        </header>

        <!-- Progress Bar -->
        <div class="bg-white border-b">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="py-4">
                    <div class="flex items-center">
                        <div class="flex items-center text-sm font-medium text-blue-600">
                            <div class="flex-shrink-0 w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                                </svg>
                            </div>
                            <span class="ml-2">Información básica</span>
                        </div>
                        <div class="flex-1 mx-4 h-0.5 bg-blue-600"></div>
                        
                        <div class="flex items-center text-sm font-medium text-blue-600">
                            <div class="flex-shrink-0 w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                                <span class="text-white text-sm">2</span>
                            </div>
                            <span class="ml-2">Configuración</span>
                        </div>
                        <div class="flex-1 mx-4 h-0.5 bg-gray-200"></div>
                        
                        <div class="flex items-center text-sm font-medium text-gray-500">
                            <div class="flex-shrink-0 w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                                <span class="text-gray-600 text-sm">3</span>
                            </div>
                            <span class="ml-2">Usuarios</span>
                        </div>
                        <div class="flex-1 mx-4 h-0.5 bg-gray-200"></div>
                        
                        <div class="flex items-center text-sm font-medium text-gray-500">
                            <div class="flex-shrink-0 w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                                <span class="text-gray-600 text-sm">4</span>
                            </div>
                            <span class="ml-2">Finalizar</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <main class="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-8">
                    <div class="mb-8">
                        <h2 class="text-2xl font-bold text-gray-900">Configuración de la Organización</h2>
                        <p class="mt-2 text-gray-600">
                            Personaliza la plataforma según las necesidades de tu organización.
                        </p>
                    </div>

                    <form class="space-y-8">
                        <!-- Branding Section -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Identidad Visual</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        Logo de la organización
                                    </label>
                                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
                                        <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                        <div class="mt-4">
                                            <button type="button" class="text-blue-600 hover:text-blue-500 font-medium">
                                                Subir logo
                                            </button>
                                            <p class="text-sm text-gray-500 mt-1">PNG, JPG hasta 2MB</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        Colores de marca
                                    </label>
                                    <div class="space-y-3">
                                        <div class="flex items-center space-x-3">
                                            <label class="text-sm text-gray-600 w-20">Primario:</label>
                                            <input type="color" value="#2563eb" class="w-12 h-8 rounded border border-gray-300">
                                            <input type="text" value="#2563eb" class="flex-1 px-3 py-1 border border-gray-300 rounded text-sm">
                                        </div>
                                        <div class="flex items-center space-x-3">
                                            <label class="text-sm text-gray-600 w-20">Secundario:</label>
                                            <input type="color" value="#64748b" class="w-12 h-8 rounded border border-gray-300">
                                            <input type="text" value="#64748b" class="flex-1 px-3 py-1 border border-gray-300 rounded text-sm">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Configuration Section -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Configuración General</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        Zona horaria
                                    </label>
                                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option>UTC-5 (América/Bogotá)</option>
                                        <option>UTC-6 (América/México)</option>
                                        <option>UTC-3 (América/Argentina)</option>
                                        <option>UTC+1 (Europa/Madrid)</option>
                                    </select>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        Idioma predeterminado
                                    </label>
                                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option>Español</option>
                                        <option>English</option>
                                        <option>Português</option>
                                        <option>Français</option>
                                    </select>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        Formato de fecha
                                    </label>
                                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option>DD/MM/YYYY</option>
                                        <option>MM/DD/YYYY</option>
                                        <option>YYYY-MM-DD</option>
                                    </select>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        Moneda
                                    </label>
                                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option>USD - Dólar estadounidense</option>
                                        <option>EUR - Euro</option>
                                        <option>COP - Peso colombiano</option>
                                        <option>MXN - Peso mexicano</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Security Settings -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Configuración de Seguridad</h3>
                            <div class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-900">Autenticación de dos factores (MFA)</h4>
                                        <p class="text-sm text-gray-500">Requerir MFA para todos los usuarios</p>
                                    </div>
                                    <button type="button" class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent bg-blue-600 transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                        <span class="translate-x-5 inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"></span>
                                    </button>
                                </div>
                                
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-900">Sesiones concurrentes</h4>
                                        <p class="text-sm text-gray-500">Limitar número de sesiones activas por usuario</p>
                                    </div>
                                    <select class="px-3 py-1 border border-gray-300 rounded text-sm">
                                        <option>Sin límite</option>
                                        <option>1 sesión</option>
                                        <option>3 sesiones</option>
                                        <option>5 sesiones</option>
                                    </select>
                                </div>
                                
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-900">Tiempo de sesión</h4>
                                        <p class="text-sm text-gray-500">Duración máxima de sesión inactiva</p>
                                    </div>
                                    <select class="px-3 py-1 border border-gray-300 rounded text-sm">
                                        <option>30 minutos</option>
                                        <option>1 hora</option>
                                        <option>4 horas</option>
                                        <option>8 horas</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Navigation Buttons -->
                        <div class="flex justify-between pt-6 border-t">
                            <button type="button" class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 font-medium">
                                Anterior
                            </button>
                            <button type="submit" class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 font-medium">
                                Continuar
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </main>
    </div>
</body>
</html>
