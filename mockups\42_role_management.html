<!DOCTYPE html>
<html lang="es" class="h-full bg-gray-50">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestión de Roles - Arroyo University</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
    </style>
</head>
<body class="h-full">
    <div class="min-h-full">
        <!-- Navigation -->
        <nav class="bg-white shadow">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <button onclick="window.location.href='04_admin_tenant_dashboard.html'" 
                                class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center hover:bg-blue-700 transition-colors">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.84L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3z"/>
                            </svg>
                        </button>
                        <h1 class="ml-3 text-xl font-semibold text-gray-900">Universidad Ejemplo</h1>
                        <nav class="ml-8 flex space-x-4">
                            <a href="04_admin_tenant_dashboard.html" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">Dashboard</a>
                            <a href="26_user_management.html" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">Usuarios</a>
                            <a href="#" class="bg-blue-100 text-blue-700 px-3 py-2 rounded-md text-sm font-medium">Roles</a>
                            <a href="40_group_management.html" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">Grupos</a>
                        </nav>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                            <span class="text-sm font-medium text-gray-700">AM</span>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="mb-8">
                <div class="flex items-center justify-between">
                    <div>
                        <h2 class="text-2xl font-bold text-gray-900">Gestión de Roles y Permisos</h2>
                        <p class="mt-1 text-gray-600">Crea y administra roles personalizados para tu organización</p>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <button onclick="openCreateRoleModal()" 
                                class="inline-flex items-center px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                            </svg>
                            Crear Rol
                        </button>
                    </div>
                </div>
            </div>

            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.5-1.5a8.38 8.38 0 01-.9 3.8 8.5 8.5 0 01-7.6 4.7 8.38 8.38 0 01-3.8-.9L3 21l1.9-8.1a8.38 8.38 0 01-.9-3.8 8.5 8.5 0 014.7-7.6 8.38 8.38 0 013.8-.9h.5a8.48 8.48 0 018 8v.5z"/>
                                </svg>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Roles Activos</dt>
                                    <dd class="text-lg font-medium text-gray-900">8</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 px-5 py-3">
                        <div class="text-sm">
                            <span class="text-blue-600 font-medium">3</span>
                            <span class="text-gray-500">personalizados</span>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"/>
                                </svg>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Usuarios Asignados</dt>
                                    <dd class="text-lg font-medium text-gray-900">1,247</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 px-5 py-3">
                        <div class="text-sm">
                            <span class="text-green-600 font-medium">100%</span>
                            <span class="text-gray-500">con roles</span>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-6 w-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.5-1.5a8.38 8.38 0 01-.9 3.8 8.5 8.5 0 01-7.6 4.7 8.38 8.38 0 01-3.8-.9L3 21l1.9-8.1a8.38 8.38 0 01-.9-3.8 8.5 8.5 0 014.7-7.6 8.38 8.38 0 013.8-.9h.5a8.48 8.48 0 018 8v.5z"/>
                                </svg>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Permisos Únicos</dt>
                                    <dd class="text-lg font-medium text-gray-900">24</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 px-5 py-3">
                        <div class="text-sm">
                            <span class="text-purple-600 font-medium">6</span>
                            <span class="text-gray-500">categorías</span>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-6 w-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                                </svg>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Grupos con Roles</dt>
                                    <dd class="text-lg font-medium text-gray-900">5</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 px-5 py-3">
                        <div class="text-sm">
                            <span class="text-yellow-600 font-medium">41.7%</span>
                            <span class="text-gray-500">del total</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Roles List -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-900">Roles Existentes</h3>
                        <div class="flex items-center space-x-2">
                            <select class="px-3 py-2 border border-gray-300 rounded-md text-sm">
                                <option>Todos los roles</option>
                                <option>Roles del sistema</option>
                                <option>Roles personalizados</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Rol
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Tipo
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Usuarios
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Permisos
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Creado
                                </th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Acciones
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <!-- Admin Role -->
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center mr-3">
                                            <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.5-1.5a8.38 8.38 0 01-.9 3.8 8.5 8.5 0 01-7.6 4.7 8.38 8.38 0 01-3.8-.9L3 21l1.9-8.1a8.38 8.38 0 01-.9-3.8 8.5 8.5 0 014.7-7.6 8.38 8.38 0 013.8-.9h.5a8.48 8.48 0 018 8v.5z"/>
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">Administrador</div>
                                            <div class="text-sm text-gray-500">Acceso completo al sistema</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        Sistema
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    3 usuarios
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    Todos (24)
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    Sistema
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <button onclick="viewRole('admin')" class="text-blue-600 hover:text-blue-900 mr-3">Ver</button>
                                    <span class="text-gray-400">No editable</span>
                                </td>
                            </tr>

                            <!-- Instructor Role -->
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">Instructor</div>
                                            <div class="text-sm text-gray-500">Crear y gestionar cursos</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        Sistema
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    45 usuarios
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    12 permisos
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    Sistema
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <button onclick="viewRole('instructor')" class="text-blue-600 hover:text-blue-900 mr-3">Ver</button>
                                    <span class="text-gray-400">No editable</span>
                                </td>
                            </tr>

                            <!-- Student Role -->
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">Estudiante</div>
                                            <div class="text-sm text-gray-500">Acceso básico a cursos</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        Sistema
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    1,199 usuarios
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    6 permisos
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    Sistema
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <button onclick="viewRole('student')" class="text-blue-600 hover:text-blue-900 mr-3">Ver</button>
                                    <span class="text-gray-400">No editable</span>
                                </td>
                            </tr>

                            <!-- Custom Role: Content Reviewer -->
                            <tr class="hover:bg-gray-50 bg-blue-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                                            <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">Revisor de Contenido</div>
                                            <div class="text-sm text-gray-500">Revisar y aprobar cursos</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                        Personalizado
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    8 usuarios
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    9 permisos
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    15 Oct 2023
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <button onclick="editRole('content-reviewer')" class="text-blue-600 hover:text-blue-900 mr-3">Editar</button>
                                    <button onclick="assignRole('content-reviewer')" class="text-green-600 hover:text-green-900 mr-3">Asignar</button>
                                    <button class="text-gray-400 hover:text-gray-600">⋯</button>
                                </td>
                            </tr>

                            <!-- Custom Role: Group Moderator -->
                            <tr class="hover:bg-gray-50 bg-blue-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center mr-3">
                                            <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">Moderador de Grupos</div>
                                            <div class="text-sm text-gray-500">Gestionar grupos y comunidades</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                        Personalizado
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    12 usuarios
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    7 permisos
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    22 Oct 2023
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <button onclick="editRole('group-moderator')" class="text-blue-600 hover:text-blue-900 mr-3">Editar</button>
                                    <button onclick="assignRole('group-moderator')" class="text-green-600 hover:text-green-900 mr-3">Asignar</button>
                                    <button class="text-gray-400 hover:text-gray-600">⋯</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>

    <!-- Create Role Modal -->
    <div id="createRoleModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-10 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-xl font-medium text-gray-900">Crear Nuevo Rol</h3>
                    <button onclick="closeCreateRoleModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                    </button>
                </div>

                <form class="space-y-6">
                    <!-- Basic Info -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Nombre del Rol *</label>
                            <input type="text" id="roleName" required
                                   placeholder="Ej: Revisor de Contenido"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Color del Rol</label>
                            <div class="flex items-center space-x-2">
                                <input type="color" id="roleColor" value="#3B82F6"
                                       class="w-12 h-10 border border-gray-300 rounded-md">
                                <span class="text-sm text-gray-500">Color para identificar el rol</span>
                            </div>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Descripción</label>
                        <textarea id="roleDescription" rows="2"
                                  placeholder="Describe las responsabilidades y propósito de este rol..."
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                    </div>

                    <!-- Permissions Section -->
                    <div class="border-t pt-6">
                        <h4 class="text-lg font-medium text-gray-900 mb-4">Permisos del Rol</h4>
                        <p class="text-sm text-gray-600 mb-6">Selecciona los permisos que tendrán los usuarios con este rol. Los permisos están organizados por categorías.</p>

                        <div class="space-y-6">
                            <!-- Course Management Permissions -->
                            <div class="bg-gray-50 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-4">
                                    <h5 class="text-md font-medium text-gray-900 flex items-center">
                                        <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                                        </svg>
                                        Gestión de Cursos
                                    </h5>
                                    <button type="button" onclick="toggleCategoryPermissions('courses')"
                                            class="text-sm text-blue-600 hover:text-blue-800">
                                        Seleccionar todos
                                    </button>
                                </div>
                                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                                    <label class="flex items-center">
                                        <input type="checkbox" name="permissions" value="courses.create"
                                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <span class="ml-2 text-sm text-gray-700">Crear cursos</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="permissions" value="courses.edit"
                                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <span class="ml-2 text-sm text-gray-700">Editar cursos</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="permissions" value="courses.delete"
                                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <span class="ml-2 text-sm text-gray-700">Eliminar cursos</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="permissions" value="courses.view_all"
                                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <span class="ml-2 text-sm text-gray-700">Ver todos los cursos</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="permissions" value="courses.publish"
                                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <span class="ml-2 text-sm text-gray-700">Publicar cursos</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="permissions" value="courses.approve"
                                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <span class="ml-2 text-sm text-gray-700">Aprobar cursos</span>
                                    </label>
                                </div>
                            </div>

                            <!-- Exam Management Permissions -->
                            <div class="bg-gray-50 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-4">
                                    <h5 class="text-md font-medium text-gray-900 flex items-center">
                                        <svg class="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                                        </svg>
                                        Gestión de Exámenes
                                    </h5>
                                    <button type="button" onclick="toggleCategoryPermissions('exams')"
                                            class="text-sm text-blue-600 hover:text-blue-800">
                                        Seleccionar todos
                                    </button>
                                </div>
                                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                                    <label class="flex items-center">
                                        <input type="checkbox" name="permissions" value="exams.create"
                                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <span class="ml-2 text-sm text-gray-700">Crear exámenes</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="permissions" value="exams.edit"
                                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <span class="ml-2 text-sm text-gray-700">Editar exámenes</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="permissions" value="exams.delete"
                                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <span class="ml-2 text-sm text-gray-700">Eliminar exámenes</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="permissions" value="exams.grade"
                                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <span class="ml-2 text-sm text-gray-700">Calificar exámenes</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="permissions" value="exams.view_results"
                                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <span class="ml-2 text-sm text-gray-700">Ver resultados</span>
                                    </label>
                                </div>
                            </div>

                            <!-- User Management Permissions -->
                            <div class="bg-gray-50 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-4">
                                    <h5 class="text-md font-medium text-gray-900 flex items-center">
                                        <svg class="w-5 h-5 text-purple-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"/>
                                        </svg>
                                        Gestión de Usuarios
                                    </h5>
                                    <button type="button" onclick="toggleCategoryPermissions('users')"
                                            class="text-sm text-blue-600 hover:text-blue-800">
                                        Seleccionar todos
                                    </button>
                                </div>
                                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                                    <label class="flex items-center">
                                        <input type="checkbox" name="permissions" value="users.create"
                                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <span class="ml-2 text-sm text-gray-700">Crear usuarios</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="permissions" value="users.edit"
                                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <span class="ml-2 text-sm text-gray-700">Editar usuarios</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="permissions" value="users.delete"
                                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <span class="ml-2 text-sm text-gray-700">Eliminar usuarios</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="permissions" value="users.view_all"
                                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <span class="ml-2 text-sm text-gray-700">Ver todos los usuarios</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="permissions" value="users.assign_roles"
                                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <span class="ml-2 text-sm text-gray-700">Asignar roles</span>
                                    </label>
                                </div>
                            </div>

                            <!-- Group Management Permissions -->
                            <div class="bg-gray-50 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-4">
                                    <h5 class="text-md font-medium text-gray-900 flex items-center">
                                        <svg class="w-5 h-5 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                                        </svg>
                                        Gestión de Grupos
                                    </h5>
                                    <button type="button" onclick="toggleCategoryPermissions('groups')"
                                            class="text-sm text-blue-600 hover:text-blue-800">
                                        Seleccionar todos
                                    </button>
                                </div>
                                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                                    <label class="flex items-center">
                                        <input type="checkbox" name="permissions" value="groups.create"
                                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <span class="ml-2 text-sm text-gray-700">Crear grupos</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="permissions" value="groups.edit"
                                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <span class="ml-2 text-sm text-gray-700">Editar grupos</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="permissions" value="groups.delete"
                                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <span class="ml-2 text-sm text-gray-700">Eliminar grupos</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="permissions" value="groups.moderate"
                                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <span class="ml-2 text-sm text-gray-700">Moderar grupos</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="permissions" value="groups.invite"
                                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <span class="ml-2 text-sm text-gray-700">Invitar miembros</span>
                                    </label>
                                </div>
                            </div>

                            <!-- Analytics Permissions -->
                            <div class="bg-gray-50 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-4">
                                    <h5 class="text-md font-medium text-gray-900 flex items-center">
                                        <svg class="w-5 h-5 text-red-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                                        </svg>
                                        Analítica y Reportes
                                    </h5>
                                    <button type="button" onclick="toggleCategoryPermissions('analytics')"
                                            class="text-sm text-blue-600 hover:text-blue-800">
                                        Seleccionar todos
                                    </button>
                                </div>
                                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                                    <label class="flex items-center">
                                        <input type="checkbox" name="permissions" value="analytics.view"
                                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <span class="ml-2 text-sm text-gray-700">Ver analítica</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="permissions" value="analytics.export"
                                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <span class="ml-2 text-sm text-gray-700">Exportar reportes</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="permissions" value="analytics.advanced"
                                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <span class="ml-2 text-sm text-gray-700">Analítica avanzada</span>
                                    </label>
                                </div>
                            </div>

                            <!-- System Permissions -->
                            <div class="bg-gray-50 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-4">
                                    <h5 class="text-md font-medium text-gray-900 flex items-center">
                                        <svg class="w-5 h-5 text-gray-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                        </svg>
                                        Sistema
                                    </h5>
                                    <button type="button" onclick="toggleCategoryPermissions('system')"
                                            class="text-sm text-blue-600 hover:text-blue-800">
                                        Seleccionar todos
                                    </button>
                                </div>
                                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                                    <label class="flex items-center">
                                        <input type="checkbox" name="permissions" value="system.settings"
                                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <span class="ml-2 text-sm text-gray-700">Configuración del sistema</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="permissions" value="system.backup"
                                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <span class="ml-2 text-sm text-gray-700">Gestionar backups</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="permissions" value="system.logs"
                                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <span class="ml-2 text-sm text-gray-700">Ver logs del sistema</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-end space-x-3 pt-6 border-t">
                        <button type="button" onclick="closeCreateRoleModal()"
                                class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                            Cancelar
                        </button>
                        <button type="button" onclick="createRole()"
                                class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                            Crear Rol
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Assign Role Modal -->
    <div id="assignRoleModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-10 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-xl font-medium text-gray-900">Asignar Rol: <span id="assignRoleName" class="text-blue-600"></span></h3>
                    <button onclick="closeAssignRoleModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                    </button>
                </div>

                <!-- Search Section -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Buscar Usuarios o Grupos</label>
                    <div class="relative">
                        <input type="text" id="searchInput"
                               placeholder="Buscar por nombre, email o grupo..."
                               class="w-full px-4 py-2 pl-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                               oninput="searchUsersAndGroups()">
                        <svg class="absolute left-3 top-2.5 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                        </svg>
                    </div>
                </div>

                <!-- Filter Tabs -->
                <div class="mb-4">
                    <div class="border-b border-gray-200">
                        <nav class="-mb-px flex space-x-8">
                            <button onclick="showAssignTab('users')" id="usersAssignTab"
                                    class="py-2 px-1 border-b-2 border-blue-500 font-medium text-sm text-blue-600">
                                Usuarios
                            </button>
                            <button onclick="showAssignTab('groups')" id="groupsAssignTab"
                                    class="py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300">
                                Grupos
                            </button>
                        </nav>
                    </div>
                </div>

                <!-- Users List -->
                <div id="usersAssignContent" class="max-h-96 overflow-y-auto">
                    <div class="space-y-2">
                        <!-- User 1 -->
                        <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                    <span class="text-sm font-medium text-blue-600">MG</span>
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-gray-900">María García</div>
                                    <div class="text-xs text-gray-500"><EMAIL></div>
                                    <div class="text-xs text-gray-400">Rol actual: Instructor</div>
                                </div>
                            </div>
                            <label class="flex items-center">
                                <input type="checkbox" name="assignUsers" value="maria-garcia"
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">Asignar</span>
                            </label>
                        </div>

                        <!-- User 2 -->
                        <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                    <span class="text-sm font-medium text-green-600">CL</span>
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-gray-900">Carlos López</div>
                                    <div class="text-xs text-gray-500"><EMAIL></div>
                                    <div class="text-xs text-gray-400">Rol actual: Estudiante</div>
                                </div>
                            </div>
                            <label class="flex items-center">
                                <input type="checkbox" name="assignUsers" value="carlos-lopez"
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">Asignar</span>
                            </label>
                        </div>

                        <!-- User 3 -->
                        <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                                    <span class="text-sm font-medium text-purple-600">AM</span>
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-gray-900">Ana Martínez</div>
                                    <div class="text-xs text-gray-500"><EMAIL></div>
                                    <div class="text-xs text-gray-400">Rol actual: Administrador</div>
                                </div>
                            </div>
                            <label class="flex items-center">
                                <input type="checkbox" name="assignUsers" value="ana-martinez"
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">Asignar</span>
                            </label>
                        </div>

                        <!-- User 4 -->
                        <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center mr-3">
                                    <span class="text-sm font-medium text-yellow-600">PS</span>
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-gray-900">Pedro Silva</div>
                                    <div class="text-xs text-gray-500"><EMAIL></div>
                                    <div class="text-xs text-gray-400">Rol actual: Instructor</div>
                                </div>
                            </div>
                            <label class="flex items-center">
                                <input type="checkbox" name="assignUsers" value="pedro-silva"
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">Asignar</span>
                            </label>
                        </div>

                        <!-- User 5 -->
                        <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center mr-3">
                                    <span class="text-sm font-medium text-red-600">LR</span>
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-gray-900">Laura Rodríguez</div>
                                    <div class="text-xs text-gray-500"><EMAIL></div>
                                    <div class="text-xs text-gray-400">Rol actual: Estudiante</div>
                                </div>
                            </div>
                            <label class="flex items-center">
                                <input type="checkbox" name="assignUsers" value="laura-rodriguez"
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">Asignar</span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Groups List -->
                <div id="groupsAssignContent" class="max-h-96 overflow-y-auto hidden">
                    <div class="space-y-2">
                        <!-- Group 1 -->
                        <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-gray-900">Grupo de Práctica IA</div>
                                    <div class="text-xs text-gray-500">45 miembros • Público</div>
                                    <div class="text-xs text-gray-400">Líder: Dra. Martínez</div>
                                </div>
                            </div>
                            <label class="flex items-center">
                                <input type="checkbox" name="assignGroups" value="ai-practice"
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">Asignar</span>
                            </label>
                        </div>

                        <!-- Group 2 -->
                        <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                                    </svg>
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-gray-900">Comunidad Agile</div>
                                    <div class="text-xs text-gray-500">32 miembros • Público</div>
                                    <div class="text-xs text-gray-400">Líder: Prof. García</div>
                                </div>
                            </div>
                            <label class="flex items-center">
                                <input type="checkbox" name="assignGroups" value="agile-community"
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">Asignar</span>
                            </label>
                        </div>

                        <!-- Group 3 -->
                        <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"/>
                                    </svg>
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-gray-900">Comunidad Full Stack</div>
                                    <div class="text-xs text-gray-500">67 miembros • Público</div>
                                    <div class="text-xs text-gray-400">Líder: Prof. Silva</div>
                                </div>
                            </div>
                            <label class="flex items-center">
                                <input type="checkbox" name="assignGroups" value="fullstack-community"
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">Asignar</span>
                            </label>
                        </div>

                        <!-- Group 4 -->
                        <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 bg-purple-50">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                                    </svg>
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-gray-900">DevOps Avanzado</div>
                                    <div class="text-xs text-gray-500">12 miembros • Privado</div>
                                    <div class="text-xs text-gray-400">Líder: Ing. López</div>
                                </div>
                            </div>
                            <label class="flex items-center">
                                <input type="checkbox" name="assignGroups" value="devops-advanced"
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">Asignar</span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Selected Summary -->
                <div id="selectedSummary" class="mt-6 p-4 bg-blue-50 rounded-lg hidden">
                    <h4 class="text-sm font-medium text-blue-900 mb-2">Seleccionados para asignar:</h4>
                    <div id="selectedList" class="text-sm text-blue-800"></div>
                </div>

                <div class="flex justify-end space-x-3 pt-6 border-t">
                    <button type="button" onclick="closeAssignRoleModal()"
                            class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                        Cancelar
                    </button>
                    <button type="button" onclick="confirmAssignRole()"
                            class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        Asignar Rol
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentRoleForAssignment = '';

        // Create Role Modal Functions
        function openCreateRoleModal() {
            document.getElementById('createRoleModal').classList.remove('hidden');
        }

        function closeCreateRoleModal() {
            document.getElementById('createRoleModal').classList.add('hidden');
            resetCreateRoleForm();
        }

        function resetCreateRoleForm() {
            document.getElementById('roleName').value = '';
            document.getElementById('roleDescription').value = '';
            document.getElementById('roleColor').value = '#3B82F6';

            // Uncheck all permissions
            const checkboxes = document.querySelectorAll('input[name="permissions"]');
            checkboxes.forEach(checkbox => checkbox.checked = false);
        }

        function toggleCategoryPermissions(category) {
            const categoryCheckboxes = document.querySelectorAll(`input[value^="${category}."]`);
            const allChecked = Array.from(categoryCheckboxes).every(cb => cb.checked);

            categoryCheckboxes.forEach(checkbox => {
                checkbox.checked = !allChecked;
            });
        }

        function createRole() {
            const roleName = document.getElementById('roleName').value;
            const roleDescription = document.getElementById('roleDescription').value;
            const roleColor = document.getElementById('roleColor').value;

            if (!roleName) {
                alert('Por favor, ingresa un nombre para el rol');
                return;
            }

            const selectedPermissions = Array.from(document.querySelectorAll('input[name="permissions"]:checked'))
                .map(cb => cb.value);

            if (selectedPermissions.length === 0) {
                alert('Por favor, selecciona al menos un permiso para el rol');
                return;
            }

            alert(`¡Rol "${roleName}" creado exitosamente!\n\nPermisos asignados: ${selectedPermissions.length}\nColor: ${roleColor}\n\nEl rol está listo para ser asignado a usuarios y grupos.`);
            closeCreateRoleModal();
        }

        // Assign Role Modal Functions
        function assignRole(roleId) {
            currentRoleForAssignment = roleId;
            document.getElementById('assignRoleName').textContent = getRoleDisplayName(roleId);
            document.getElementById('assignRoleModal').classList.remove('hidden');
        }

        function closeAssignRoleModal() {
            document.getElementById('assignRoleModal').classList.add('hidden');
            resetAssignRoleForm();
        }

        function resetAssignRoleForm() {
            document.getElementById('searchInput').value = '';

            // Uncheck all users and groups
            const userCheckboxes = document.querySelectorAll('input[name="assignUsers"]');
            const groupCheckboxes = document.querySelectorAll('input[name="assignGroups"]');

            userCheckboxes.forEach(cb => cb.checked = false);
            groupCheckboxes.forEach(cb => cb.checked = false);

            updateSelectedSummary();
        }

        function showAssignTab(tabName) {
            // Hide all content
            document.getElementById('usersAssignContent').classList.add('hidden');
            document.getElementById('groupsAssignContent').classList.add('hidden');

            // Reset tab styles
            document.getElementById('usersAssignTab').className = 'py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300';
            document.getElementById('groupsAssignTab').className = 'py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300';

            // Show selected content and update tab style
            if (tabName === 'users') {
                document.getElementById('usersAssignContent').classList.remove('hidden');
                document.getElementById('usersAssignTab').className = 'py-2 px-1 border-b-2 border-blue-500 font-medium text-sm text-blue-600';
            } else if (tabName === 'groups') {
                document.getElementById('groupsAssignContent').classList.remove('hidden');
                document.getElementById('groupsAssignTab').className = 'py-2 px-1 border-b-2 border-blue-500 font-medium text-sm text-blue-600';
            }
        }

        function searchUsersAndGroups() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();

            // Search in users
            const userItems = document.querySelectorAll('#usersAssignContent > div > div');
            userItems.forEach(item => {
                const text = item.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    item.style.display = 'flex';
                } else {
                    item.style.display = 'none';
                }
            });

            // Search in groups
            const groupItems = document.querySelectorAll('#groupsAssignContent > div > div');
            groupItems.forEach(item => {
                const text = item.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    item.style.display = 'flex';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        function updateSelectedSummary() {
            const selectedUsers = Array.from(document.querySelectorAll('input[name="assignUsers"]:checked'));
            const selectedGroups = Array.from(document.querySelectorAll('input[name="assignGroups"]:checked'));

            const totalSelected = selectedUsers.length + selectedGroups.length;
            const summaryDiv = document.getElementById('selectedSummary');
            const listDiv = document.getElementById('selectedList');

            if (totalSelected > 0) {
                summaryDiv.classList.remove('hidden');

                let summaryText = '';
                if (selectedUsers.length > 0) {
                    summaryText += `${selectedUsers.length} usuario(s)`;
                }
                if (selectedGroups.length > 0) {
                    if (summaryText) summaryText += ' y ';
                    summaryText += `${selectedGroups.length} grupo(s)`;
                }

                listDiv.textContent = summaryText;
            } else {
                summaryDiv.classList.add('hidden');
            }
        }

        function confirmAssignRole() {
            const selectedUsers = Array.from(document.querySelectorAll('input[name="assignUsers"]:checked'));
            const selectedGroups = Array.from(document.querySelectorAll('input[name="assignGroups"]:checked'));

            const totalSelected = selectedUsers.length + selectedGroups.length;

            if (totalSelected === 0) {
                alert('Por favor, selecciona al menos un usuario o grupo para asignar el rol');
                return;
            }

            const roleName = getRoleDisplayName(currentRoleForAssignment);
            alert(`¡Rol "${roleName}" asignado exitosamente!\n\nAsignado a:\n- ${selectedUsers.length} usuario(s)\n- ${selectedGroups.length} grupo(s)\n\nLos usuarios recibirán notificaciones sobre sus nuevos permisos.`);
            closeAssignRoleModal();
        }

        // Utility Functions
        function getRoleDisplayName(roleId) {
            const roleNames = {
                'content-reviewer': 'Revisor de Contenido',
                'group-moderator': 'Moderador de Grupos',
                'admin': 'Administrador',
                'instructor': 'Instructor',
                'student': 'Estudiante'
            };
            return roleNames[roleId] || roleId;
        }

        function viewRole(roleId) {
            alert(`Viendo detalles del rol: ${getRoleDisplayName(roleId)}`);
        }

        function editRole(roleId) {
            alert(`Editando rol: ${getRoleDisplayName(roleId)}`);
        }

        // Event Listeners
        document.addEventListener('DOMContentLoaded', function() {
            // Add event listeners for checkboxes to update summary
            const userCheckboxes = document.querySelectorAll('input[name="assignUsers"]');
            const groupCheckboxes = document.querySelectorAll('input[name="assignGroups"]');

            userCheckboxes.forEach(cb => {
                cb.addEventListener('change', updateSelectedSummary);
            });

            groupCheckboxes.forEach(cb => {
                cb.addEventListener('change', updateSelectedSummary);
            });
        });
    </script>
</body>
</html>
